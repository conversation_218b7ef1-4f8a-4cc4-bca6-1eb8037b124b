{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 12116, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 12116, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 12116, "tid": 980, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 12116, "tid": 980, "ts": 1751471784597813, "dur": 639, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 12116, "tid": 980, "ts": 1751471784600559, "dur": 818, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 12116, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 12116, "tid": 1, "ts": 1751471783017855, "dur": 5484, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 12116, "tid": 1, "ts": 1751471783023345, "dur": 111969, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 12116, "tid": 1, "ts": 1751471783135336, "dur": 1223506, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 12116, "tid": 980, "ts": 1751471784601383, "dur": 20, "ph": "X", "name": "", "args": {}}, {"pid": 12116, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783015886, "dur": 6124, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783022014, "dur": 1566917, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783022903, "dur": 2850, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783025762, "dur": 1730, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783027499, "dur": 273, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783027779, "dur": 25, "ph": "X", "name": "ProcessMessages 20547", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783027806, "dur": 41, "ph": "X", "name": "ReadAsync 20547", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783027854, "dur": 4, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783027860, "dur": 109, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783027972, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783027974, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028059, "dur": 5, "ph": "X", "name": "ProcessMessages 1966", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028066, "dur": 54, "ph": "X", "name": "ReadAsync 1966", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028123, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028158, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028164, "dur": 28, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028196, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028198, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028227, "dur": 2, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028230, "dur": 103, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028340, "dur": 7, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028353, "dur": 45, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028403, "dur": 3, "ph": "X", "name": "ProcessMessages 1363", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028408, "dur": 98, "ph": "X", "name": "ReadAsync 1363", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028512, "dur": 2, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028516, "dur": 33, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028552, "dur": 3, "ph": "X", "name": "ProcessMessages 1363", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028557, "dur": 97, "ph": "X", "name": "ReadAsync 1363", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028666, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028713, "dur": 4, "ph": "X", "name": "ProcessMessages 1958", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028719, "dur": 100, "ph": "X", "name": "ReadAsync 1958", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028825, "dur": 2, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028830, "dur": 35, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028866, "dur": 3, "ph": "X", "name": "ProcessMessages 1474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028871, "dur": 97, "ph": "X", "name": "ReadAsync 1474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783028984, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029056, "dur": 4, "ph": "X", "name": "ProcessMessages 1528", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029062, "dur": 34, "ph": "X", "name": "ReadAsync 1528", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029099, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029101, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029159, "dur": 3, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029165, "dur": 280, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029450, "dur": 9, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029466, "dur": 105, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029580, "dur": 7, "ph": "X", "name": "ProcessMessages 5124", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029589, "dur": 27, "ph": "X", "name": "ReadAsync 5124", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029618, "dur": 2, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029621, "dur": 130, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029762, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029764, "dur": 31, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029797, "dur": 3, "ph": "X", "name": "ProcessMessages 1825", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029802, "dur": 136, "ph": "X", "name": "ReadAsync 1825", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029947, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029993, "dur": 2, "ph": "X", "name": "ProcessMessages 1475", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783029997, "dur": 108, "ph": "X", "name": "ReadAsync 1475", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030110, "dur": 32, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030148, "dur": 4, "ph": "X", "name": "ProcessMessages 1568", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030154, "dur": 92, "ph": "X", "name": "ReadAsync 1568", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030250, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030317, "dur": 9, "ph": "X", "name": "ProcessMessages 2080", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030332, "dur": 32, "ph": "X", "name": "ReadAsync 2080", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030366, "dur": 2, "ph": "X", "name": "ProcessMessages 1570", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030370, "dur": 79, "ph": "X", "name": "ReadAsync 1570", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030461, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030464, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030487, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030490, "dur": 27, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030519, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030522, "dur": 31, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030556, "dur": 2, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030562, "dur": 52, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030619, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030647, "dur": 2, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030651, "dur": 17, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030671, "dur": 1, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030673, "dur": 15, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030690, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030692, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030714, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030716, "dur": 26, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030748, "dur": 2, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030751, "dur": 76, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030830, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030832, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030849, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030852, "dur": 26, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030881, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030883, "dur": 23, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030909, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030912, "dur": 33, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030956, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030959, "dur": 37, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783030999, "dur": 2, "ph": "X", "name": "ProcessMessages 1004", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031003, "dur": 18, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031027, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031029, "dur": 48, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031080, "dur": 2, "ph": "X", "name": "ProcessMessages 1466", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031084, "dur": 56, "ph": "X", "name": "ReadAsync 1466", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031147, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031185, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031190, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031210, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031213, "dur": 15, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031234, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031238, "dur": 23, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031268, "dur": 2, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031271, "dur": 16, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031289, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031291, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031312, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031314, "dur": 33, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031349, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031352, "dur": 29, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031384, "dur": 2, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031387, "dur": 13, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031401, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031404, "dur": 138, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031547, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031575, "dur": 2, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031578, "dur": 22, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031603, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031606, "dur": 31, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031639, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031641, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031665, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031667, "dur": 25, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031697, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031701, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031748, "dur": 4, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031754, "dur": 24, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031780, "dur": 2, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031784, "dur": 18, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031805, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031808, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031830, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031833, "dur": 20, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031857, "dur": 2, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031861, "dur": 20, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031882, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031885, "dur": 18, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031907, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031911, "dur": 14, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031927, "dur": 1, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031929, "dur": 18, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031950, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031952, "dur": 26, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031981, "dur": 2, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783031985, "dur": 16, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032003, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032006, "dur": 19, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032028, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032032, "dur": 16, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032049, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032052, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032075, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032078, "dur": 23, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032107, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032110, "dur": 23, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032135, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032142, "dur": 30, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032175, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032178, "dur": 26, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032207, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032209, "dur": 31, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032243, "dur": 2, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032247, "dur": 24, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032273, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032276, "dur": 17, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032295, "dur": 1, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032298, "dur": 24, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032328, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032331, "dur": 27, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032360, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032363, "dur": 22, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032388, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032390, "dur": 18, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032411, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032413, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032437, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032440, "dur": 24, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032466, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032468, "dur": 22, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032497, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032500, "dur": 19, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032521, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032523, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032545, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032547, "dur": 20, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032569, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032572, "dur": 17, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032596, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032599, "dur": 25, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032626, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032629, "dur": 21, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032652, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032655, "dur": 20, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032678, "dur": 2, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032681, "dur": 19, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032701, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032704, "dur": 17, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032723, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032725, "dur": 23, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032751, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032753, "dur": 27, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032786, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032789, "dur": 22, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032817, "dur": 1, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032820, "dur": 21, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032847, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032849, "dur": 19, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032871, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032875, "dur": 23, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032899, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032902, "dur": 15, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032922, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032926, "dur": 20, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032950, "dur": 2, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032959, "dur": 20, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032985, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783032988, "dur": 17, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033007, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033010, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033037, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033041, "dur": 22, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033065, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033068, "dur": 19, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033089, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033092, "dur": 24, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033121, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033148, "dur": 2, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033152, "dur": 22, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033177, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033180, "dur": 19, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033201, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033204, "dur": 26, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033234, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033237, "dur": 25, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033267, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033271, "dur": 24, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033298, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033301, "dur": 13, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033315, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033318, "dur": 15, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033337, "dur": 25, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033364, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033367, "dur": 23, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033396, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033400, "dur": 27, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033430, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033433, "dur": 16, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033451, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033454, "dur": 25, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033480, "dur": 5, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033487, "dur": 18, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033508, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033510, "dur": 24, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033537, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033539, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033560, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033562, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033583, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033586, "dur": 26, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033614, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033617, "dur": 20, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033639, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033642, "dur": 18, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033662, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033665, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033689, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033692, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033717, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033720, "dur": 18, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033740, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033743, "dur": 15, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033759, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033762, "dur": 13, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033776, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033779, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033799, "dur": 23, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033825, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033828, "dur": 22, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033852, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033855, "dur": 16, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033874, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033876, "dur": 18, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033896, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033899, "dur": 10, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033911, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783033913, "dur": 189, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034104, "dur": 3, "ph": "X", "name": "ProcessMessages 2454", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034139, "dur": 56, "ph": "X", "name": "ReadAsync 2454", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034198, "dur": 183, "ph": "X", "name": "ProcessMessages 3209", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034384, "dur": 58, "ph": "X", "name": "ReadAsync 3209", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034445, "dur": 6, "ph": "X", "name": "ProcessMessages 5482", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034453, "dur": 52, "ph": "X", "name": "ReadAsync 5482", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034517, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034521, "dur": 27, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034552, "dur": 2, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034556, "dur": 18, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034576, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034578, "dur": 20, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034601, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034604, "dur": 19, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034625, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034627, "dur": 24, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034657, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034663, "dur": 15, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034681, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034683, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034706, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034708, "dur": 21, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034732, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034735, "dur": 17, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034756, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034760, "dur": 28, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034790, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034798, "dur": 22, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034821, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034824, "dur": 20, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034847, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034850, "dur": 20, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034872, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034875, "dur": 29, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034906, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034909, "dur": 31, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034943, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034946, "dur": 27, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034976, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783034979, "dur": 21, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035001, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035004, "dur": 18, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035023, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035029, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035051, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035054, "dur": 24, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035080, "dur": 5, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035088, "dur": 19, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035109, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035112, "dur": 27, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035142, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035145, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035175, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035177, "dur": 17, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035197, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035199, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035226, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035229, "dur": 20, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035252, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035255, "dur": 19, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035277, "dur": 20, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035300, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035303, "dur": 18, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035322, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035325, "dur": 20, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035351, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035354, "dur": 18, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035379, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035382, "dur": 20, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035404, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035407, "dur": 10, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035419, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035421, "dur": 14, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035436, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035438, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035467, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035470, "dur": 29, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035502, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035504, "dur": 21, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035527, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035530, "dur": 34, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035567, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035569, "dur": 21, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035592, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035595, "dur": 17, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035614, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035616, "dur": 17, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035637, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035639, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035662, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035665, "dur": 18, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035689, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035692, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035715, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035718, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035745, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035747, "dur": 24, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035774, "dur": 8, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035785, "dur": 21, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035812, "dur": 2, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035815, "dur": 16, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035837, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035840, "dur": 28, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035870, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035873, "dur": 21, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035896, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035899, "dur": 21, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035922, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035925, "dur": 21, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035949, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035951, "dur": 17, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035972, "dur": 20, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035995, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783035997, "dur": 20, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036019, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036022, "dur": 19, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036044, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036046, "dur": 21, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036069, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036071, "dur": 17, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036090, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036093, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036111, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036114, "dur": 23, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036143, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036147, "dur": 21, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036176, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036179, "dur": 31, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036212, "dur": 2, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036215, "dur": 21, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036238, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036241, "dur": 22, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036264, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036268, "dur": 33, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036304, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036307, "dur": 27, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036337, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036339, "dur": 30, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036372, "dur": 2, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036375, "dur": 16, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036392, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036396, "dur": 24, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036422, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036425, "dur": 31, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036458, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036461, "dur": 35, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036498, "dur": 2, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036502, "dur": 18, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036522, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036525, "dur": 23, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036550, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036553, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036579, "dur": 2, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036583, "dur": 22, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036607, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036610, "dur": 25, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036637, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036640, "dur": 23, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036665, "dur": 1, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036668, "dur": 18, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036687, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036689, "dur": 23, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036715, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036720, "dur": 14, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036736, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036738, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036765, "dur": 21, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036788, "dur": 2, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036792, "dur": 19, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036814, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036817, "dur": 21, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036841, "dur": 2, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036844, "dur": 30, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036877, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036880, "dur": 20, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036903, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036906, "dur": 24, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036932, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036935, "dur": 12, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036949, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036951, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036972, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783036979, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037000, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037002, "dur": 23, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037028, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037031, "dur": 27, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037064, "dur": 2, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037067, "dur": 23, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037093, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037095, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037118, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037120, "dur": 20, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037143, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037146, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037171, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037173, "dur": 30, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037206, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037209, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037239, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037241, "dur": 23, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037267, "dur": 2, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037271, "dur": 22, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037299, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037302, "dur": 17, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037322, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037324, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037343, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037346, "dur": 24, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037372, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037375, "dur": 30, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037408, "dur": 2, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037411, "dur": 27, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037440, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037443, "dur": 17, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037461, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037464, "dur": 23, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037494, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037498, "dur": 122, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037623, "dur": 2, "ph": "X", "name": "ProcessMessages 1010", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037627, "dur": 30, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037659, "dur": 2, "ph": "X", "name": "ProcessMessages 1844", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037663, "dur": 37, "ph": "X", "name": "ReadAsync 1844", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037703, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037706, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037729, "dur": 2, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037732, "dur": 25, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037759, "dur": 5, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037768, "dur": 33, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037804, "dur": 2, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037808, "dur": 21, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037834, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037838, "dur": 21, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037862, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037865, "dur": 43, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037910, "dur": 2, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037914, "dur": 27, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037944, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037947, "dur": 28, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037977, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783037980, "dur": 29, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038012, "dur": 2, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038015, "dur": 21, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038039, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038041, "dur": 18, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038062, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038064, "dur": 23, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038090, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038092, "dur": 16, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038112, "dur": 55, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038170, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038173, "dur": 20, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038195, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038198, "dur": 15, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038215, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038217, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038296, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038298, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038320, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038323, "dur": 23, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038349, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038352, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038378, "dur": 2, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038382, "dur": 20, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038404, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038407, "dur": 16, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038425, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038427, "dur": 17, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038446, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038449, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038510, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038534, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038537, "dur": 37, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038584, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038587, "dur": 28, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038617, "dur": 3, "ph": "X", "name": "ProcessMessages 1367", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038622, "dur": 15, "ph": "X", "name": "ReadAsync 1367", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038640, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038642, "dur": 17, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038662, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038664, "dur": 53, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038721, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038747, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038750, "dur": 18, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038774, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038777, "dur": 55, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038837, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038863, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038866, "dur": 19, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038889, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038892, "dur": 53, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038950, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038981, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783038984, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039010, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039013, "dur": 50, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039068, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039100, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039103, "dur": 20, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039129, "dur": 2, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039133, "dur": 49, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039186, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039210, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039213, "dur": 25, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039240, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039243, "dur": 59, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039305, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039330, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039333, "dur": 23, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039360, "dur": 7, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039369, "dur": 46, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039419, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039444, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039447, "dur": 19, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039473, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039476, "dur": 49, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039529, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039555, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039558, "dur": 18, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039578, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039581, "dur": 56, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039641, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039665, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039668, "dur": 19, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039690, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039692, "dur": 56, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039753, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039777, "dur": 3, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039781, "dur": 19, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039803, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039806, "dur": 52, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039863, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039888, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039892, "dur": 33, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039935, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039939, "dur": 32, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783039975, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040021, "dur": 2, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040024, "dur": 16, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040042, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040044, "dur": 37, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040086, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040117, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040121, "dur": 17, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040140, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040142, "dur": 52, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040199, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040227, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040231, "dur": 19, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040252, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040255, "dur": 53, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040313, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040336, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040338, "dur": 20, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040363, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040367, "dur": 55, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040426, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040456, "dur": 2, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040460, "dur": 18, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040480, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040483, "dur": 48, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040536, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040562, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040564, "dur": 23, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040594, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040597, "dur": 46, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040648, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040668, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040671, "dur": 18, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040692, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040694, "dur": 14, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040710, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040712, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040767, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040790, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040793, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040814, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040816, "dur": 56, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040877, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040911, "dur": 2, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040915, "dur": 17, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040934, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040937, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040986, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783040988, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041015, "dur": 2, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041018, "dur": 18, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041042, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041044, "dur": 58, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041115, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041118, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041150, "dur": 2, "ph": "X", "name": "ProcessMessages 1246", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041154, "dur": 24, "ph": "X", "name": "ReadAsync 1246", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041181, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041184, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041214, "dur": 2, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041217, "dur": 18, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041238, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041240, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041296, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041331, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041334, "dur": 15, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041351, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041353, "dur": 51, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041409, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041439, "dur": 2, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041442, "dur": 17, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041461, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041463, "dur": 24, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041489, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041492, "dur": 16, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041510, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041512, "dur": 20, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041535, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041538, "dur": 12, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041552, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041556, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041576, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041579, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041631, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041655, "dur": 2, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041658, "dur": 18, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041681, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041685, "dur": 53, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041742, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041765, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041767, "dur": 21, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041791, "dur": 6, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041799, "dur": 43, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041850, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041853, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041882, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041885, "dur": 17, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041906, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041909, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041962, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041982, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783041985, "dur": 16, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042003, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042005, "dur": 23, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042035, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042038, "dur": 16, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042057, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042059, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042113, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042152, "dur": 2, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042155, "dur": 21, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042178, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042180, "dur": 36, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042227, "dur": 2, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042231, "dur": 35, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042279, "dur": 3, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042289, "dur": 27, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042320, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042322, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042344, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042347, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042372, "dur": 5, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042379, "dur": 15, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042396, "dur": 5, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042403, "dur": 35, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042443, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042466, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042468, "dur": 22, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042496, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042500, "dur": 19, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042521, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042523, "dur": 23, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042548, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042551, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042572, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042575, "dur": 18, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042595, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042602, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042652, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042654, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042678, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042681, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042705, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042708, "dur": 54, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042766, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042791, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042799, "dur": 32, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042842, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042845, "dur": 25, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042876, "dur": 2, "ph": "X", "name": "ProcessMessages 1232", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042879, "dur": 17, "ph": "X", "name": "ReadAsync 1232", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042899, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042901, "dur": 16, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042922, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042926, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783042976, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043006, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043009, "dur": 21, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043032, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043035, "dur": 47, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043086, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043112, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043115, "dur": 18, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043135, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043137, "dur": 56, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043196, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043198, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043230, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043233, "dur": 23, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043259, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043280, "dur": 13, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043296, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043298, "dur": 17, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043317, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043320, "dur": 23, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043345, "dur": 3, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043349, "dur": 19, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043374, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043377, "dur": 21, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043404, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043408, "dur": 19, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043429, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043431, "dur": 17, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043450, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043452, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043514, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043549, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043552, "dur": 18, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043572, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043575, "dur": 46, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043625, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043653, "dur": 2, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043658, "dur": 20, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043680, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043683, "dur": 60, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043755, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043757, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043779, "dur": 4, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043785, "dur": 52, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043841, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043872, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043875, "dur": 18, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043899, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043902, "dur": 44, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043950, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043971, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043973, "dur": 21, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783043996, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044003, "dur": 32, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044038, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044041, "dur": 31, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044075, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044078, "dur": 25, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044105, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044108, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044157, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044188, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044191, "dur": 21, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044215, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044217, "dur": 46, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044267, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044291, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044295, "dur": 23, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044320, "dur": 5, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044327, "dur": 49, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044381, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044409, "dur": 2, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044413, "dur": 24, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044439, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044442, "dur": 20, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044464, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044467, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044516, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044518, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044539, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044542, "dur": 18, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044562, "dur": 1, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044564, "dur": 32, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044598, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044600, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044631, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044633, "dur": 36, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044674, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044706, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044709, "dur": 18, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044734, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044737, "dur": 47, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044788, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044830, "dur": 2, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044833, "dur": 16, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044855, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044858, "dur": 41, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044901, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044904, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044933, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044936, "dur": 21, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044959, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783044961, "dur": 49, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045015, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045039, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045043, "dur": 26, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045072, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045074, "dur": 24, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045101, "dur": 2, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045104, "dur": 20, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045126, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045129, "dur": 16, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045150, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045153, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045172, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045174, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045235, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045263, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045266, "dur": 18, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045290, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045293, "dur": 53, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045349, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045351, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045379, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045382, "dur": 15, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045403, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045406, "dur": 17, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045425, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045427, "dur": 52, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045483, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045505, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045508, "dur": 21, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045532, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045538, "dur": 15, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045556, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045559, "dur": 37, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045600, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045634, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045638, "dur": 22, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045662, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045665, "dur": 42, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045711, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045745, "dur": 2, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045748, "dur": 27, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045778, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045781, "dur": 35, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045820, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045844, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045847, "dur": 16, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045867, "dur": 4, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045872, "dur": 51, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045930, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045957, "dur": 2, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045960, "dur": 18, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045984, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783045987, "dur": 49, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046041, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046067, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046070, "dur": 18, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046093, "dur": 2, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046097, "dur": 49, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046151, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046180, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046183, "dur": 18, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046206, "dur": 7, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046215, "dur": 43, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046263, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046286, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046290, "dur": 22, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046314, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046316, "dur": 17, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046336, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046338, "dur": 48, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046390, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046419, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046422, "dur": 22, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046447, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046449, "dur": 48, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046502, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046530, "dur": 3, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046535, "dur": 17, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046554, "dur": 5, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046562, "dur": 48, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046614, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046638, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046641, "dur": 19, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046666, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046669, "dur": 27, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046698, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046700, "dur": 28, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046731, "dur": 2, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046734, "dur": 23, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046759, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046762, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046781, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046784, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046831, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046887, "dur": 7, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046897, "dur": 34, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046936, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783046940, "dur": 56, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047002, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047041, "dur": 2, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047045, "dur": 27, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047075, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047078, "dur": 39, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047121, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047126, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047163, "dur": 2, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047166, "dur": 21, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047190, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047193, "dur": 51, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047247, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047249, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047279, "dur": 2, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047282, "dur": 16, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047299, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047302, "dur": 55, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047364, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047394, "dur": 2, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047397, "dur": 21, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047421, "dur": 2, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047424, "dur": 17, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047443, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047446, "dur": 18, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047466, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047469, "dur": 13, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047483, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047485, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047510, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047512, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047570, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047572, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047601, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047603, "dur": 18, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047626, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047630, "dur": 47, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047681, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047711, "dur": 2, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047714, "dur": 15, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047731, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047734, "dur": 52, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047789, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047792, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047821, "dur": 2, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047824, "dur": 16, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047848, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047852, "dur": 47, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047904, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047929, "dur": 2, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047932, "dur": 16, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047950, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783047953, "dur": 64, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048021, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048051, "dur": 2, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048054, "dur": 18, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048075, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048078, "dur": 48, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048130, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048158, "dur": 2, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048162, "dur": 16, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048180, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048182, "dur": 52, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048237, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048240, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048267, "dur": 3, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048271, "dur": 19, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048294, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048297, "dur": 49, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048350, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048379, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048383, "dur": 17, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048402, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048404, "dur": 59, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048466, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048468, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048488, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048491, "dur": 19, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048512, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048515, "dur": 16, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048533, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048535, "dur": 14, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048552, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048554, "dur": 23, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048580, "dur": 6, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048589, "dur": 16, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048607, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048609, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048629, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048631, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048687, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048714, "dur": 2, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048717, "dur": 16, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048735, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048737, "dur": 57, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048798, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048801, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048826, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048830, "dur": 18, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048850, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048853, "dur": 15, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048870, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048872, "dur": 23, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048898, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048901, "dur": 23, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048928, "dur": 6, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048936, "dur": 28, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048967, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048970, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048986, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783048988, "dur": 41, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049035, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049063, "dur": 2, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049067, "dur": 24, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049094, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049098, "dur": 20, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049119, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049122, "dur": 19, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049144, "dur": 3, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049149, "dur": 22, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049175, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049178, "dur": 17, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049196, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049198, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049260, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049280, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049283, "dur": 24, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049309, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049312, "dur": 63, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049378, "dur": 3, "ph": "X", "name": "ProcessMessages 1548", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049383, "dur": 16, "ph": "X", "name": "ReadAsync 1548", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049401, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049403, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049455, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049457, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049488, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049491, "dur": 60, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049556, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049560, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783049597, "dur": 431, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050035, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050093, "dur": 14, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050111, "dur": 39, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050158, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050165, "dur": 30, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050209, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050220, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050249, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050252, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050286, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050292, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050323, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050328, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050359, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050367, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050397, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050402, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050433, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050438, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050467, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050471, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050499, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050505, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050535, "dur": 4, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050542, "dur": 31, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050577, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050583, "dur": 25, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050612, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050617, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050643, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050649, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050676, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050680, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050702, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050707, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050736, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050742, "dur": 21, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050766, "dur": 4, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050772, "dur": 18, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050793, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050798, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050833, "dur": 7, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050843, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050875, "dur": 5, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050883, "dur": 26, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050914, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050921, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050964, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783050972, "dur": 40, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051015, "dur": 5, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051022, "dur": 28, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051054, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051061, "dur": 42, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051109, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051114, "dur": 31, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051149, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051156, "dur": 36, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051196, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051203, "dur": 37, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051245, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051250, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051293, "dur": 6, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051301, "dur": 29, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051333, "dur": 4, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051338, "dur": 25, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051370, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051376, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051405, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051412, "dur": 38, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051456, "dur": 6, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051465, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051512, "dur": 8, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051523, "dur": 67, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051594, "dur": 8, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051604, "dur": 31, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051641, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051647, "dur": 28, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051679, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051686, "dur": 28, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051719, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051726, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051768, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051774, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051807, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051811, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051846, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051850, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051885, "dur": 5, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051893, "dur": 24, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051920, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051926, "dur": 28, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051959, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051965, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783051999, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052005, "dur": 23, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052032, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052037, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052074, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052081, "dur": 31, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052116, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052123, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052149, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052155, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052186, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052192, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052221, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052227, "dur": 20, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052252, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052257, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052289, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052295, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052326, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052333, "dur": 27, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052363, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052371, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052398, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052402, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052431, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052435, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052462, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783052467, "dur": 8121, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783060601, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783060608, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783060660, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783060667, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783060707, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783060712, "dur": 117, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783060833, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783060836, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783060859, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783060862, "dur": 642, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061510, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061513, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061542, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061545, "dur": 91, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061640, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061653, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061676, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061680, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061924, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061927, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061958, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783061962, "dur": 157, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062124, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062128, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062154, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062159, "dur": 83, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062251, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062273, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062276, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062304, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062309, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062430, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062433, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062452, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062455, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062513, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062516, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062532, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062535, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062560, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062563, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062580, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062585, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062692, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062708, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062711, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062797, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062813, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062816, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062896, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062900, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062920, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062924, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062943, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062946, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062961, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062965, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062987, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783062990, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063004, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063007, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063108, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063111, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063132, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063136, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063190, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063194, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063212, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063216, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063249, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063252, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063269, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063275, "dur": 45, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063324, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063327, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063341, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063344, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063370, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063374, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063413, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063416, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063438, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063443, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063461, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063469, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063492, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063496, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063515, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063519, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063537, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063541, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063583, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063587, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063617, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063621, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063646, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063649, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063673, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063677, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063798, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063823, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063826, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063847, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063849, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063870, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063873, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063896, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063899, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063915, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063919, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063939, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063943, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063958, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783063960, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064122, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064125, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064146, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064150, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064232, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064234, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064255, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064259, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064283, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064287, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064308, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064311, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064451, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064453, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064474, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064478, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064496, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064499, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064548, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064551, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064576, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064581, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064623, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064629, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064662, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064666, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064691, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064696, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064750, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064753, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064773, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064777, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064797, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064801, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064854, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064859, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064877, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064881, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064901, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064904, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064925, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064930, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064950, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783064953, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065002, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065020, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065023, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065074, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065078, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065102, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065106, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065127, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065130, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065175, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065201, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065204, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065347, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065350, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065373, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065377, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065524, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065527, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065547, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065550, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065570, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065573, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065592, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065595, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065613, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065617, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065704, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065729, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065733, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065767, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065770, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065792, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065796, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065825, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065830, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065978, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783065982, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066005, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066009, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066033, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066038, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066056, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066060, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066078, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066081, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066103, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066107, "dur": 112, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066224, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066228, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066252, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066256, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066270, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066274, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066293, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066298, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066418, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066421, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066444, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066447, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066462, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066465, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066483, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066487, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066525, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066542, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066544, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066564, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066568, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066599, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066602, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066619, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066622, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066654, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066657, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066675, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066678, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066696, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066699, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066752, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066768, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066771, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066804, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066808, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066827, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066830, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066847, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066850, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066881, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066884, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066903, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066906, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066982, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783066985, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067004, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067007, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067022, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067024, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067038, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067041, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067058, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067061, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067086, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067090, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067108, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067110, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067150, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067154, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067184, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067190, "dur": 44, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067241, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067245, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067279, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067282, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067301, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067304, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067328, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067331, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067473, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067477, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067508, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067512, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067557, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067561, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067603, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067609, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783067644, "dur": 1046, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783068700, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783068755, "dur": 12, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783068770, "dur": 200, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783068976, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783068980, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069013, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069018, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069087, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069092, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069126, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069131, "dur": 194, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069332, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069335, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069372, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069377, "dur": 163, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069549, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069624, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069627, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069676, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069679, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069706, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069710, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069745, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069768, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069772, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069807, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069811, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069847, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069850, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069884, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069889, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069927, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069932, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069957, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069960, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069987, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783069991, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070040, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070044, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070065, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070069, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070163, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070167, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070203, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070208, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070262, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070266, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070295, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070300, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070321, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070326, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070415, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070418, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070510, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070513, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070546, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070550, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070751, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070754, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070780, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070783, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070849, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070853, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070945, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070949, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070972, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070974, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783070997, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071000, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071108, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071112, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071198, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071202, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071295, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071299, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071375, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071379, "dur": 367, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071754, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071759, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071804, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071809, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071911, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071915, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071949, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783071952, "dur": 185, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072141, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072145, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072190, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072196, "dur": 174, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072376, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072379, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072416, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072420, "dur": 506, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072929, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072932, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072958, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783072961, "dur": 831, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783073799, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783073805, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783073835, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783073840, "dur": 44392, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783118245, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783118250, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783118305, "dur": 1598, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783119909, "dur": 6509, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126430, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126439, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126482, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126488, "dur": 41, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126536, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126541, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126565, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126568, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126583, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126586, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126613, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126618, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126643, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126648, "dur": 127, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126779, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126781, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126808, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126812, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126847, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126851, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126872, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783126876, "dur": 592, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783127471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783127474, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783127501, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783127506, "dur": 379, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783127889, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783127892, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783127919, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783127924, "dur": 818, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783128745, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783128748, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783128765, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783128768, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783128909, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783128912, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783128942, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783128948, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783128975, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783128979, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129016, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129021, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129181, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129184, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129203, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129206, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129331, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129334, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129349, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129352, "dur": 195, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129552, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129556, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129586, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129590, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129691, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129694, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129719, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129724, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129799, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129802, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129825, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783129829, "dur": 672, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783130505, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783130508, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783130535, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783130539, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783130562, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783130565, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783130583, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783130586, "dur": 551, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131141, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131145, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131162, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131166, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131190, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131194, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131222, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131227, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131245, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131248, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131298, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131302, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131323, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131326, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131359, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131361, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131381, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783131385, "dur": 759, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132152, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132158, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132187, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132191, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132310, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132331, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132334, "dur": 223, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132562, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132566, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132585, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132588, "dur": 251, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132845, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132850, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132870, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132874, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132893, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783132896, "dur": 421, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133321, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133324, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133354, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133358, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133492, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133495, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133530, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133534, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133671, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133693, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133696, "dur": 242, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133941, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133946, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133966, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783133969, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134119, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134122, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134139, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134143, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134399, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134402, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134419, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134422, "dur": 323, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134750, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134754, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134778, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134783, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134986, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783134988, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135005, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135009, "dur": 200, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135213, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135216, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135233, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135236, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135272, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135276, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135294, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135297, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135528, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135530, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135545, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135548, "dur": 185, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135739, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135742, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135759, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135762, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135803, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135806, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135828, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783135832, "dur": 332, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136168, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136171, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136197, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136201, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136273, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136277, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136296, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136299, "dur": 164, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136467, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136470, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136486, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783136489, "dur": 542, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137034, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137046, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137071, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137075, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137091, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137094, "dur": 599, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137698, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137701, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137719, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137723, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137895, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137898, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137927, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783137931, "dur": 384, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138318, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138321, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138338, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138341, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138364, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138367, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138384, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138387, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138410, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138413, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138427, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138430, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138484, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138488, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138504, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138507, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138619, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138623, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138640, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138643, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138669, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138674, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138693, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138695, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138712, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138715, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138769, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138773, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138791, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138794, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138808, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138811, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138837, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138840, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138864, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138867, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138893, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138910, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138913, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138931, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138934, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138956, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138976, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138980, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138996, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783138999, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139016, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139020, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139102, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139107, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139220, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139223, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139243, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139247, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139268, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139271, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139292, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139296, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139313, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139316, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139335, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139339, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139358, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139361, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139384, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139388, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139416, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139420, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139440, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139444, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139472, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139476, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139502, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139509, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139525, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139528, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139552, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139556, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139595, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139598, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139616, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139619, "dur": 201, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139824, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139828, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139847, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139851, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139868, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139871, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139891, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139897, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139918, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139922, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139943, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139947, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139970, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139974, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139994, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783139997, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140014, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140020, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140039, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140043, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140060, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140063, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140083, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140086, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140106, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140110, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140132, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140137, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140170, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140176, "dur": 24, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140205, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140212, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140237, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140241, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140265, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140269, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140285, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140287, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140303, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140306, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140327, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140330, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140350, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140354, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140380, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140402, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140407, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140438, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140443, "dur": 31, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140478, "dur": 3, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140483, "dur": 22, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140508, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140513, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140543, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140548, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140577, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140581, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140604, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140608, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140631, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140634, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140658, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140662, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140688, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140695, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140723, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140727, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140755, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140760, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140803, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140807, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140827, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140831, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140870, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783140874, "dur": 160, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141040, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141044, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141069, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141074, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141109, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141116, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141159, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141166, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141260, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141268, "dur": 117, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141396, "dur": 7, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141405, "dur": 162, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141581, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141584, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141690, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471783141696, "dur": 1121831, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784263543, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784263549, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784263668, "dur": 27, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784263698, "dur": 4435, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784268142, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784268147, "dur": 123, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784268277, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784268282, "dur": 52104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784320413, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784320423, "dur": 133, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784320564, "dur": 7, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784320574, "dur": 89587, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784410173, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784410179, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784410238, "dur": 27, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784410268, "dur": 16314, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784426595, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784426613, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784426725, "dur": 12, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784426738, "dur": 28768, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784455515, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784455521, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784455554, "dur": 28, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784455584, "dur": 17571, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784473165, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784473172, "dur": 123, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784473307, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784473313, "dur": 1136, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784474486, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784474492, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784474613, "dur": 57, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784474676, "dur": 82794, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784557481, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784557487, "dur": 153, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784557647, "dur": 13, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784557662, "dur": 1416, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784559085, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784559090, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784559138, "dur": 31, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784559172, "dur": 591, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784559777, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784559784, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784559885, "dur": 663, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751471784560555, "dur": 28301, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 12116, "tid": 980, "ts": 1751471784601406, "dur": 5439, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 12116, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 12116, "tid": 8589934592, "ts": 1751471783013529, "dur": 1345369, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 12116, "tid": 8589934592, "ts": 1751471784358901, "dur": 10, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 12116, "tid": 8589934592, "ts": 1751471784358912, "dur": 1253, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 12116, "tid": 980, "ts": 1751471784606853, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 12116, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 12116, "tid": 4294967296, "ts": 1751471782996316, "dur": 1593716, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 12116, "tid": 4294967296, "ts": 1751471782999601, "dur": 6883, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 12116, "tid": 4294967296, "ts": 1751471784590262, "dur": 5050, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 12116, "tid": 4294967296, "ts": 1751471784592963, "dur": 121, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 12116, "tid": 4294967296, "ts": 1751471784595432, "dur": 39, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 12116, "tid": 980, "ts": 1751471784606867, "dur": 16, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751471783021483, "dur": 1653, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751471783023147, "dur": 1789, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751471783025049, "dur": 152, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751471783025202, "dur": 347, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751471783026329, "dur": 329, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751471783027370, "dur": 2312, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751471783029748, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_189352823CD57890.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751471783029919, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751471783030135, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751471783030290, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751471783030461, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_2FAFF9F1339AD54A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751471783030607, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751471783030778, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751471783030926, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751471783031106, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751471783031408, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751471783031528, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751471783031700, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751471783031877, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751471783032060, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_D2DCB817177C37A1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751471783032275, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751471783032464, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751471783032652, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751471783032983, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751471783036326, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751471783025570, "dur": 25808, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751471783051395, "dur": 1509569, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751471784560966, "dur": 270, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751471784561237, "dur": 82, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751471784561342, "dur": 88, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751471784561604, "dur": 23374, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751471783025739, "dur": 25662, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783051485, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751471783051885, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_878D99D4DAFDE3CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751471783052792, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751471783052919, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751471783053118, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751471783053406, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751471783053629, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751471783053683, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783053951, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10054408285822421624.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751471783054267, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783054492, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783055920, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751471783056497, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.OAuth.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751471783055290, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783057083, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783057330, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\TimelineNavigator.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751471783057298, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783058282, "dur": 1014, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Graphs\\DynamicMatrixMaterialSlot.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751471783058125, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783059339, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783059601, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783059813, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783060018, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783060238, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783060463, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783060698, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783060915, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783061132, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783061349, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783061567, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783061833, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783062059, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783062877, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783063424, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783064175, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751471783064348, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783064894, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751471783065173, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783065262, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783066155, "dur": 865, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783067025, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783067556, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783067686, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751471783067908, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783068600, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783069008, "dur": 1390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783070399, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751471783070516, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783070862, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783071713, "dur": 2376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783074089, "dur": 51837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783125927, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783128391, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783130618, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783130824, "dur": 2426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783133251, "dur": 798, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783134056, "dur": 2880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783136937, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783137134, "dur": 2942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783140077, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471783140239, "dur": 2473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751471783142758, "dur": 1285508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751471784428297, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751471784428268, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751471784428386, "dur": 132609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783025791, "dur": 25633, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783051427, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783051889, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_60DEBCA51ABA1468.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783052049, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE214F9E6C036FF9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783052179, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_B9FBC03C59DCE731.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783052803, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751471783053005, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751471783053193, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751471783053416, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751471783053653, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783053912, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751471783054262, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783054478, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783055377, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783056155, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783056374, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783056655, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783057236, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783057451, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783057655, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783057857, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783058076, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783058289, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783058497, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783058727, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783058948, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783059186, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783059406, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783059635, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783059865, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783060109, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783060324, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783060568, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783060770, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783060966, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783061330, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783061551, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783061774, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783061996, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783062351, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783062584, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783063401, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783063850, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783064055, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783064771, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783064865, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783065170, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783065332, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783066046, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783066188, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783066367, "dur": 1997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783068365, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783068729, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_B033D5B74DD6DC2D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783068802, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783069070, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783069593, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783069743, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783070364, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783070464, "dur": 1258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783071782, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783071953, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783072984, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783073084, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783073674, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783073771, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783074082, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751471783074226, "dur": 51713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783125943, "dur": 2410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783128396, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783130725, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783130859, "dur": 2184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783133044, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783133156, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783135581, "dur": 2395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783137977, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783138081, "dur": 2449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751471783140530, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783140961, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783141170, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783141389, "dur": 947, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783142402, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751471783142525, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783142599, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751471783142973, "dur": 1417988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783025840, "dur": 25601, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783051444, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783051845, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_A1C14A714FD44069.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783052027, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_EAC3CE9D642351D7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783052102, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_34D7E2BEC34818D5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783052540, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783052687, "dur": 9836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783062600, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783062743, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783063416, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783063502, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783063848, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783064042, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783064751, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783064929, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783065173, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783065784, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783065842, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783066011, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783066185, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783066398, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783066665, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783067344, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783067529, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783067691, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751471783067896, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783069001, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783069235, "dur": 2487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783071722, "dur": 2370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783074092, "dur": 54217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783128310, "dur": 4711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783133059, "dur": 2316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783135406, "dur": 2343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783137750, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783138190, "dur": 2357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751471783140635, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783141208, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783141347, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783141739, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751471783141887, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783142125, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783142241, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783142594, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471783142767, "dur": 1332068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751471784474854, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751471784474837, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751471784475038, "dur": 1223, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751471784476269, "dur": 84723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783025781, "dur": 25637, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783051421, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783051743, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AB9848F6D5CE3BFE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783051941, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_33DE0D3A006A6424.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783052024, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_767627B5731476EF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783052105, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4C3484A57A05DA64.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783052656, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751471783053187, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751471783053319, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751471783053595, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751471783053670, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783054236, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783054442, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783054864, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783056066, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783056301, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783056652, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783057189, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783057407, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783057605, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783057828, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783058037, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783058249, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783058451, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783058664, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783058885, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783059100, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783059314, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783059543, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783059771, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783059981, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783060320, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783060549, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783060755, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783060956, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783061188, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783061391, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783061609, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783061819, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783062030, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783062717, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783063402, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783063862, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783064165, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783064350, "dur": 1328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751471783065679, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783066040, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783066207, "dur": 634, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783066845, "dur": 1645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751471783068536, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783068670, "dur": 1247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751471783069979, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783070128, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751471783070638, "dur": 1086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783071724, "dur": 2358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783074085, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751471783074240, "dur": 51690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783125936, "dur": 2783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751471783128770, "dur": 2407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751471783131178, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783131717, "dur": 2720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751471783134478, "dur": 2684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751471783137196, "dur": 2348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751471783139545, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783139614, "dur": 2163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751471783141777, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783141840, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783142173, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783142485, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783142606, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751471783143030, "dur": 1417923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783025758, "dur": 25653, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783051474, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783051799, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783052073, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783052829, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751471783053191, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751471783053264, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751471783053557, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751471783053626, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751471783053692, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783053805, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751471783054154, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2669672505582432988.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751471783054210, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783054278, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783054516, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783055796, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783056031, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783056247, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783056464, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783056685, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783057252, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783057465, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783057671, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783057871, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783058085, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783058427, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783058639, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783058915, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783059175, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783059456, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783059700, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783060006, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\RenderGraph\\RenderGraphCompilationCache.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751471783059916, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783060676, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783060874, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783061092, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783061311, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783061571, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783061816, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783062036, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783062818, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783063405, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783063857, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783064058, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783064862, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783065109, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783065515, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783066296, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783066460, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783066571, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783066773, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783066975, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783067123, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783067990, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783068177, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783068788, "dur": 826, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783069630, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783069726, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783070044, "dur": 1661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783071708, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783071881, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783072680, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751471783072809, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783073188, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783074088, "dur": 51837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783125931, "dur": 2325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783128308, "dur": 3691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783132000, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783132486, "dur": 2712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783135198, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783135859, "dur": 4399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783140299, "dur": 2573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751471783142873, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751471783143013, "dur": 1417946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783025805, "dur": 25625, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783051432, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751471783051798, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_05122CAABA8E3B59.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751471783052084, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751471783052867, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751471783053101, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751471783053251, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751471783053406, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751471783053678, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783053861, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751471783054246, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783054442, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783055478, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783055690, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783055911, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783056133, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783056369, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783056675, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783057206, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783057416, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783057619, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783057826, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783058028, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783058246, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783058443, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783058648, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783058869, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783059144, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783059356, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783059587, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783059798, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783060064, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783060276, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783060489, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783060740, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783060978, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783061633, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783061857, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783062081, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783062518, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783062580, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783063406, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783064201, "dur": 1003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783065246, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751471783065509, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783066168, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783066584, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751471783066789, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751471783066997, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783068247, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783068516, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783068572, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751471783068956, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783070281, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751471783070445, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783071262, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751471783071412, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783071983, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783072196, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751471783072372, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783072898, "dur": 1205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783074103, "dur": 51843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783125949, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783128323, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783128558, "dur": 2254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783130813, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783130917, "dur": 2104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783133024, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783133111, "dur": 2751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783135863, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783136320, "dur": 2393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783138713, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783138954, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751471783141532, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783141792, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783142065, "dur": 504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783142608, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751471783143082, "dur": 1417890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783025829, "dur": 25607, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783051438, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751471783052016, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_366576C77F483F0B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751471783052092, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751471783052580, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783052870, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751471783052935, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751471783053410, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751471783053494, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751471783053687, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783054097, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8775554482886297096.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751471783054255, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783054366, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783054700, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783055608, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783055823, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783056051, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783056266, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783056528, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783057060, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783057271, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783057482, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783057794, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783058059, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783058268, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783058474, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783058687, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783058900, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783059109, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783059322, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783059989, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783060211, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783060546, "dur": 545, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Ensure\\EnsureThat.Comparables.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751471783060425, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783061183, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783061593, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@91020cbfae56\\Editor\\UI\\TilePaletteBrushInspectorPopup.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751471783061396, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783062142, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783062367, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783062576, "dur": 824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783063400, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783063859, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751471783064052, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783064681, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783065032, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783065458, "dur": 1199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783066658, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783066799, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751471783066993, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783068130, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783068450, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783068607, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783068904, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751471783069253, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783069386, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783069921, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751471783070167, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783070998, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751471783071148, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783071705, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751471783071831, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783072244, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751471783072351, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783072784, "dur": 1313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783074097, "dur": 51867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783125966, "dur": 2402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783128373, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783128697, "dur": 2354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783131102, "dur": 2098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783133236, "dur": 2579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783135816, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783136040, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783138386, "dur": 2448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751471783140835, "dur": 483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783141457, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783141960, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783142101, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783142591, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471783142738, "dur": 1219361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471784363571, "dur": 189, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751471784363760, "dur": 1020, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1751471784364780, "dur": 54, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1751471784362104, "dur": 2733, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751471784364837, "dur": 196132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783025847, "dur": 25600, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783051450, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751471783051754, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_966AC74B818A5F90.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751471783051855, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_5AE8CAD8193F1AE4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751471783052034, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_32AC050EF3A70AF5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751471783052086, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783052166, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_099E8E7D5E4735AD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751471783052343, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783052733, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751471783052827, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751471783053080, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751471783053190, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751471783053364, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751471783053570, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751471783053651, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783054176, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3248707927565818496.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751471783054258, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783054498, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783055330, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783056132, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783056368, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783056690, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\GraphView\\Views\\Properties\\Vector2PropertyRM.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751471783056603, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783057462, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783057676, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783057893, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783058167, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783058378, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783058582, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783058794, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783059031, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783059255, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783059477, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783059705, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783059918, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783060148, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783060358, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783060591, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783060798, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783061035, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783061261, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783061476, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783061696, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783061915, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783062126, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783062480, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783062591, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783063407, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783063851, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751471783064037, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783065555, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783065874, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751471783066052, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783066692, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751471783066868, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783067782, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783068010, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783068469, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751471783068758, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783068817, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751471783068984, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783069508, "dur": 2201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783071709, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783072684, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751471783072834, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783073167, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783074094, "dur": 51841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783125937, "dur": 2424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783128389, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783130658, "dur": 2032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783132693, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783133091, "dur": 2559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783135651, "dur": 1011, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783136668, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783138895, "dur": 1352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751471783140252, "dur": 3103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751471783143406, "dur": 1417591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783025862, "dur": 25593, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783051458, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751471783051772, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F01F6C04D397C746.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751471783051965, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_A01C5835F2B3673A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751471783052055, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783052164, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B72398FA1A1CE63D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751471783052921, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751471783053015, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751471783053355, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751471783053495, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751471783053695, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783053865, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751471783053978, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4730270780318326332.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751471783054251, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783054457, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783055478, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783055947, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783056177, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783056400, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783056620, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783057205, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783057451, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783057809, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783058284, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783058480, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783058702, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783058929, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783059176, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783059404, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783059637, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783059859, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783060090, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783060303, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783060543, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783060757, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783060960, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783061179, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783061391, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783061614, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783061834, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783062077, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783062722, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783063425, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783063861, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751471783064054, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783064483, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783065269, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751471783065498, "dur": 1546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783067045, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783067265, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783067950, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783068005, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751471783068202, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783068682, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783069081, "dur": 2193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783071275, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751471783071397, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783071685, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783071778, "dur": 2308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783074087, "dur": 51835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783125925, "dur": 2427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783128386, "dur": 2828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783131215, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783131608, "dur": 2555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783134218, "dur": 2709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783136928, "dur": 793, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751471783137727, "dur": 2779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783140543, "dur": 2619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751471783143194, "dur": 1417806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783025884, "dur": 25577, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783051463, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751471783051783, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783051840, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_692349F0772110A2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751471783052087, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751471783052870, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751471783053118, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751471783053243, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751471783053431, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751471783053496, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751471783053647, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783053814, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751471783053947, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6091109579032960000.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751471783054032, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783054182, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11474151696266288451.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751471783054348, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783054619, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783055396, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783055547, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783055746, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783055982, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783056204, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783056414, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783056653, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783057196, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783057424, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783057637, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783057847, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783058062, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783058263, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783058477, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783058701, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783058925, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783059161, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783059380, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783059613, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783059818, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783060046, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783060255, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783060667, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783060872, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783061104, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783061306, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783061620, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783061856, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783062079, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783062837, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783063419, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783063863, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751471783064040, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751471783064807, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783064883, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751471783065434, "dur": 1608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751471783067043, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783067493, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751471783068210, "dur": 1255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783069498, "dur": 2223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783071721, "dur": 2363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783074086, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751471783074312, "dur": 51646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783125960, "dur": 2405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751471783128388, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751471783128447, "dur": 2355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751471783130855, "dur": 3730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751471783134586, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783134796, "dur": 2574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751471783137370, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783137451, "dur": 2756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751471783140208, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751471783140403, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751471783143077, "dur": 1417912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783025901, "dur": 25566, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783051470, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751471783051739, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_24AD401155842AFB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751471783052018, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_CD971E07FD450EB6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751471783052090, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751471783052793, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751471783053002, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751471783053150, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751471783053318, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751471783053493, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751471783053653, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783053809, "dur": 399, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751471783054269, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783054488, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783055583, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783055804, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783056037, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783056260, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783056497, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783057042, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783057250, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783057483, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783057679, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783057891, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783058118, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783058447, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783058668, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783059106, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783059331, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783059562, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783059784, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783060001, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783060226, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783060435, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783060648, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783060856, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783061074, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783061290, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783061503, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783061714, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783061941, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783062154, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783062366, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783062580, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783063400, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783063856, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751471783064046, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783064563, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783064723, "dur": 1087, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783065815, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783066555, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783066920, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783067525, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783067612, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751471783067801, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783067991, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751471783068172, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751471783068332, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783068910, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783069019, "dur": 1981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783071001, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751471783071146, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783071591, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783072075, "dur": 2020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783074095, "dur": 51859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783125956, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783128425, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783128503, "dur": 2711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783131253, "dur": 2524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783133778, "dur": 1453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783135238, "dur": 2693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783137931, "dur": 1065, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783139003, "dur": 3311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751471783142447, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783142639, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751471783143208, "dur": 1417788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783025923, "dur": 25606, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783051529, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751471783051880, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_EB030C3D9E9F35E6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751471783052054, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751471783052596, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751471783052921, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751471783053144, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751471783053320, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751471783053635, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751471783053695, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783053835, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751471783054027, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18297721446069157131.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751471783054119, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1212262423713005172.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751471783054271, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783054491, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783055382, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783056389, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783056684, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783057232, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783057457, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783057657, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783057863, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783058136, "dur": 583, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Graphs\\Texture2DInputMaterialSlot.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751471783058094, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783059013, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Runtime\\Utilities\\OutputEventHandlers\\VFXOutputEventAbstractHandler.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751471783058873, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783059740, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783060011, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783060238, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783060459, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783060682, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783060890, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783061105, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783061311, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783061533, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783061757, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783062028, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783062402, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783062572, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783063399, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783063909, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751471783064182, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783064435, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751471783065104, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783065577, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751471783065774, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751471783065996, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783066195, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751471783066477, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751471783067316, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783067740, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1751471783069050, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783069136, "dur": 216, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783069554, "dur": 50550, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1751471783125923, "dur": 2998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751471783128922, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783129383, "dur": 3513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751471783132896, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783133282, "dur": 3592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751471783136908, "dur": 2556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751471783139465, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471783139850, "dur": 2837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751471783142740, "dur": 1222109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751471784364852, "dur": 196116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783025938, "dur": 25554, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783051494, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751471783052071, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751471783052198, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_06CB192F9C3927A4.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751471783052685, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783052794, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751471783052953, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751471783053144, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751471783053384, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751471783053631, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783054299, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783054589, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783055452, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783055658, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783055887, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783056112, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783056337, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783056671, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783057202, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783057426, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783057654, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783057861, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783058070, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783058278, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783058489, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783058705, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783058919, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783059144, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783059361, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783059615, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783059844, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783060528, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Events\\EventHooks.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751471783060372, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783061125, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783061340, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783061558, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783061789, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783061933, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783062340, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783062574, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783063403, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783063858, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751471783064062, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751471783065111, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783065198, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783065362, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751471783065621, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783065729, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751471783066378, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783066474, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751471783066699, "dur": 1112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751471783067812, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783067944, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751471783068141, "dur": 846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751471783068988, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783069096, "dur": 2614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783071710, "dur": 1968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783073679, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751471783073788, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751471783074239, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751471783074799, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751471783075594, "dur": 56, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471783075968, "dur": 1189393, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751471784269771, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751471784269459, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751471784270422, "dur": 49044, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751471784269960, "dur": 50451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751471784321577, "dur": 220, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751471784322446, "dur": 89557, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751471784428241, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751471784428233, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751471784428336, "dur": 132634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783025952, "dur": 25544, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783051498, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751471783051770, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1237F680F70DA85A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751471783051844, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_68F0E05FD8235E34.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751471783051948, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_5C2FAC469D562E01.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751471783052068, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751471783052682, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751471783052858, "dur": 9567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751471783062515, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783062601, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783063422, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783064203, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751471783064351, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783064612, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751471783065381, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751471783065757, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751471783066567, "dur": 869, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783067441, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751471783068308, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783068402, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783068936, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751471783069088, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751471783069597, "dur": 2114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783071711, "dur": 2380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783074092, "dur": 51836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783125930, "dur": 2094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751471783128025, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783128533, "dur": 2804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751471783131338, "dur": 1076, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783132420, "dur": 4099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751471783136520, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783136928, "dur": 2523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751471783139452, "dur": 826, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783140288, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751471783142665, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751471783143441, "dur": 1417552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783025971, "dur": 25544, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783051517, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751471783051852, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_17E3136A60FE24E1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751471783052015, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_88313D863DE50351.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751471783052173, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_634E261A11766DA3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751471783052671, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751471783052942, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751471783053056, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751471783053261, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751471783053328, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751471783053497, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751471783053631, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751471783053688, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783054265, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783054498, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783055539, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783055739, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783055971, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783056188, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783056389, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783056620, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783057141, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783057384, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783057584, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783057786, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783058084, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783058289, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783058509, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783058719, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783058943, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783059283, "dur": 735, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnCancel.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751471783059178, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783060140, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783060353, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783060589, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783060787, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783060988, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783061199, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783061589, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@91020cbfae56\\Editor\\ObjectCreationMenu\\DefaultAssetCreation.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751471783061416, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783062144, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783062375, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783062571, "dur": 836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783063407, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783064179, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751471783065006, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783065179, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751471783065451, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751471783066146, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783066205, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751471783066387, "dur": 1144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751471783067532, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783068017, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783068186, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751471783068379, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751471783069046, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783069146, "dur": 2570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783071717, "dur": 2373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783074090, "dur": 51859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783125952, "dur": 3814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751471783129804, "dur": 2463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751471783132267, "dur": 940, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783133214, "dur": 2345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751471783135605, "dur": 2339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751471783137945, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783138094, "dur": 3039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751471783141134, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783141409, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783142139, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783142446, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783142503, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783142582, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471783142667, "dur": 1126826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471784269515, "dur": 49985, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751471784269495, "dur": 51351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751471784322071, "dur": 277, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751471784322614, "dur": 134761, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751471784474804, "dur": 84432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751471784474797, "dur": 84441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751471784559255, "dur": 1671, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751471783025990, "dur": 25529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783051521, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751471783051876, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5214F7A6C172998.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751471783052100, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783052171, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_239A276499DBD1EB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751471783052832, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751471783053008, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751471783053248, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751471783053399, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751471783053555, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751471783053697, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783054228, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783054427, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783054617, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783055442, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783055626, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783055846, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783056073, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783056294, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783056546, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783057072, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783057330, "dur": 827, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\TimelineWindow_TimeArea.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751471783057291, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783058310, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783058519, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783058722, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783058953, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783059185, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783059421, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783059656, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783059880, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783060103, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783060301, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783060520, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783060750, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783060954, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783061174, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783061399, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783061810, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783062047, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783062748, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783063405, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783063854, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751471783064040, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783064211, "dur": 1155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783065412, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783065549, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751471783065708, "dur": 1069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783066836, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783067500, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751471783067732, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783068383, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783068967, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751471783069147, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783069201, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783069739, "dur": 1968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783071708, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751471783071846, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783072225, "dur": 1873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783074099, "dur": 51844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783125945, "dur": 2339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783128285, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783128354, "dur": 3068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783131470, "dur": 3186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783134657, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783134764, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783137084, "dur": 570, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751471783137661, "dur": 2637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783140334, "dur": 2574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751471783142963, "dur": 1418023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751471784588971, "dur": 1368, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 12116, "tid": 980, "ts": 1751471784607483, "dur": 2798, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 12116, "tid": 980, "ts": 1751471784610448, "dur": 1826, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 12116, "tid": 980, "ts": 1751471784599417, "dur": 13542, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}