{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 12116, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 12116, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 12116, "tid": 12, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 12116, "tid": 12, "ts": 1751465885994265, "dur": 683, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 12116, "tid": 12, "ts": 1751465885997636, "dur": 1002, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 12116, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 12116, "tid": 1, "ts": 1751465885496873, "dur": 10502, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 12116, "tid": 1, "ts": 1751465885507381, "dur": 130520, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 12116, "tid": 1, "ts": 1751465885637916, "dur": 309320, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 12116, "tid": 12, "ts": 1751465885998644, "dur": 1277, "ph": "X", "name": "", "args": {}}, {"pid": 12116, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885494311, "dur": 4387, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885498702, "dur": 483879, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885499547, "dur": 5947, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885505506, "dur": 603, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885506117, "dur": 4039, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885510169, "dur": 185, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885510360, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885510405, "dur": 732, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885511176, "dur": 21095, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885532285, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885532295, "dur": 391, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885532693, "dur": 569, "ph": "X", "name": "ProcessMessages 2448", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533268, "dur": 229, "ph": "X", "name": "ReadAsync 2448", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533500, "dur": 23, "ph": "X", "name": "ProcessMessages 20543", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533525, "dur": 41, "ph": "X", "name": "ReadAsync 20543", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533569, "dur": 3, "ph": "X", "name": "ProcessMessages 1771", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533573, "dur": 29, "ph": "X", "name": "ReadAsync 1771", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533609, "dur": 4, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533615, "dur": 33, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533650, "dur": 2, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533653, "dur": 25, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533681, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533683, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533710, "dur": 2, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533713, "dur": 20, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533736, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533739, "dur": 28, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533769, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533771, "dur": 24, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533799, "dur": 2, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533802, "dur": 19, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533823, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533826, "dur": 24, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533856, "dur": 3, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533862, "dur": 28, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533892, "dur": 5, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533898, "dur": 16, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533916, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533919, "dur": 22, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533943, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533946, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533973, "dur": 2, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885533977, "dur": 23, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534002, "dur": 2, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534006, "dur": 23, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534032, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534034, "dur": 21, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534057, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534060, "dur": 24, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534087, "dur": 2, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534091, "dur": 21, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534113, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534116, "dur": 29, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534151, "dur": 4, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534157, "dur": 276, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534437, "dur": 3, "ph": "X", "name": "ProcessMessages 1069", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534442, "dur": 76, "ph": "X", "name": "ReadAsync 1069", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534522, "dur": 8, "ph": "X", "name": "ProcessMessages 6097", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534532, "dur": 31, "ph": "X", "name": "ReadAsync 6097", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534566, "dur": 2, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534569, "dur": 30, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534603, "dur": 2, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534607, "dur": 23, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534632, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534635, "dur": 19, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534657, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534660, "dur": 28, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534692, "dur": 2, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534696, "dur": 24, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534722, "dur": 2, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534725, "dur": 16, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534744, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534746, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534770, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534776, "dur": 31, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534810, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534814, "dur": 24, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534840, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534843, "dur": 23, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534870, "dur": 2, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534873, "dur": 18, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534893, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534896, "dur": 24, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534924, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534927, "dur": 21, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534950, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534953, "dur": 16, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534971, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534973, "dur": 19, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534995, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885534997, "dur": 22, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535022, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535024, "dur": 16, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535044, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535046, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535077, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535080, "dur": 24, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535106, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535109, "dur": 18, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535129, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535133, "dur": 26, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535162, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535166, "dur": 25, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535193, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535195, "dur": 21, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535220, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535223, "dur": 26, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535251, "dur": 2, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535254, "dur": 22, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535279, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535283, "dur": 21, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535308, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535310, "dur": 22, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535334, "dur": 2, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535338, "dur": 16, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535356, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535358, "dur": 18, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535378, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535381, "dur": 16, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535400, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535402, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535432, "dur": 2, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535436, "dur": 30, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535469, "dur": 2, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535473, "dur": 21, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535497, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535500, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535521, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535524, "dur": 20, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535546, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535549, "dur": 20, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535571, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535574, "dur": 17, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535593, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535595, "dur": 18, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535616, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535618, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535640, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535642, "dur": 20, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535665, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535667, "dur": 18, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535687, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535690, "dur": 14, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535707, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535709, "dur": 15, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535728, "dur": 19, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535749, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535751, "dur": 16, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535770, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535772, "dur": 21, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535795, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535798, "dur": 17, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535817, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535819, "dur": 14, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535835, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535837, "dur": 15, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535854, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535856, "dur": 18, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535877, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535879, "dur": 19, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535901, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535904, "dur": 19, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535926, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535928, "dur": 12, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535942, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535944, "dur": 15, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535962, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535964, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535985, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885535988, "dur": 24, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536014, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536020, "dur": 23, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536047, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536051, "dur": 84, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536137, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536140, "dur": 44, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536189, "dur": 4, "ph": "X", "name": "ProcessMessages 2019", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536194, "dur": 16, "ph": "X", "name": "ReadAsync 2019", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536212, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536215, "dur": 19, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536236, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536239, "dur": 24, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536269, "dur": 4, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536276, "dur": 95, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536376, "dur": 2, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536380, "dur": 107, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536489, "dur": 3, "ph": "X", "name": "ProcessMessages 2202", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536493, "dur": 107, "ph": "X", "name": "ReadAsync 2202", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536605, "dur": 4, "ph": "X", "name": "ProcessMessages 1862", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536610, "dur": 82, "ph": "X", "name": "ReadAsync 1862", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536696, "dur": 4, "ph": "X", "name": "ProcessMessages 2072", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536702, "dur": 127, "ph": "X", "name": "ReadAsync 2072", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536836, "dur": 18, "ph": "X", "name": "ProcessMessages 1973", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536858, "dur": 28, "ph": "X", "name": "ReadAsync 1973", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536890, "dur": 2, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536895, "dur": 36, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536935, "dur": 2, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885536940, "dur": 100, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537043, "dur": 2, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537046, "dur": 104, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537155, "dur": 4, "ph": "X", "name": "ProcessMessages 2257", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537160, "dur": 45, "ph": "X", "name": "ReadAsync 2257", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537210, "dur": 4, "ph": "X", "name": "ProcessMessages 2525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537216, "dur": 25, "ph": "X", "name": "ReadAsync 2525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537245, "dur": 2, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537249, "dur": 41, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537295, "dur": 3, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537302, "dur": 73, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537377, "dur": 2, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537380, "dur": 33, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537416, "dur": 3, "ph": "X", "name": "ProcessMessages 1947", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537420, "dur": 17, "ph": "X", "name": "ReadAsync 1947", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537441, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537444, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537468, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537471, "dur": 25, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537498, "dur": 2, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537502, "dur": 20, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537524, "dur": 2, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537527, "dur": 22, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537551, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537554, "dur": 20, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537578, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537582, "dur": 25, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537609, "dur": 1, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537612, "dur": 21, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537637, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537641, "dur": 31, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537675, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537678, "dur": 14, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537694, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537696, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537810, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537812, "dur": 29, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537844, "dur": 2, "ph": "X", "name": "ProcessMessages 1625", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537848, "dur": 14, "ph": "X", "name": "ReadAsync 1625", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537866, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537943, "dur": 8, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537956, "dur": 33, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537992, "dur": 2, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885537996, "dur": 19, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538017, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538020, "dur": 36, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538061, "dur": 2, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538065, "dur": 29, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538100, "dur": 3, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538106, "dur": 44, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538153, "dur": 2, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538157, "dur": 29, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538191, "dur": 3, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538196, "dur": 25, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538223, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538226, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538252, "dur": 1, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538255, "dur": 14, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538271, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538274, "dur": 19, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538295, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538298, "dur": 28, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538328, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538331, "dur": 27, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538365, "dur": 4, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538374, "dur": 36, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538413, "dur": 2, "ph": "X", "name": "ProcessMessages 1323", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538417, "dur": 25, "ph": "X", "name": "ReadAsync 1323", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538447, "dur": 3, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538453, "dur": 29, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538486, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538490, "dur": 19, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538511, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538514, "dur": 14, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538529, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538531, "dur": 17, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538553, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538557, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538580, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538583, "dur": 18, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538603, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538606, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538628, "dur": 17, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538648, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538652, "dur": 21, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538675, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538678, "dur": 19, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538699, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538702, "dur": 19, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538723, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538726, "dur": 18, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538746, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538749, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538771, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538773, "dur": 26, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538801, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538804, "dur": 22, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538828, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538831, "dur": 20, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538853, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538855, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538877, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538879, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538903, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538906, "dur": 19, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538928, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538931, "dur": 24, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538957, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538960, "dur": 15, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538979, "dur": 16, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885538997, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539000, "dur": 34, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539045, "dur": 2, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539049, "dur": 22, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539073, "dur": 2, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539076, "dur": 18, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539097, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539099, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539120, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539122, "dur": 18, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539142, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539145, "dur": 17, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539164, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539167, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539185, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539187, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539206, "dur": 17, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539225, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539228, "dur": 20, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539250, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539252, "dur": 17, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539272, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539274, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539295, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539297, "dur": 14, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539314, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539316, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539335, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539337, "dur": 18, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539357, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539360, "dur": 17, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539379, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539381, "dur": 15, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539399, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539401, "dur": 22, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539425, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539428, "dur": 13, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539443, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539446, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539471, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539473, "dur": 19, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539495, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539497, "dur": 18, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539517, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539520, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539542, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539544, "dur": 14, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539561, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539563, "dur": 35, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539600, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539620, "dur": 23, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539646, "dur": 198, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539850, "dur": 63, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539917, "dur": 6, "ph": "X", "name": "ProcessMessages 3888", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539925, "dur": 20, "ph": "X", "name": "ReadAsync 3888", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539947, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539950, "dur": 22, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539974, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885539977, "dur": 31, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540011, "dur": 2, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540015, "dur": 41, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540059, "dur": 2, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540063, "dur": 23, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540088, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540091, "dur": 20, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540113, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540116, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540138, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540141, "dur": 18, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540161, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540164, "dur": 125, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540291, "dur": 3, "ph": "X", "name": "ProcessMessages 2260", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540296, "dur": 19, "ph": "X", "name": "ReadAsync 2260", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540316, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540319, "dur": 28, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540350, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540352, "dur": 23, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540379, "dur": 3, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540384, "dur": 15, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540401, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540403, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540424, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540427, "dur": 22, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540453, "dur": 2, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540457, "dur": 33, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540493, "dur": 2, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540496, "dur": 18, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540517, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540520, "dur": 28, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540551, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540554, "dur": 38, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540599, "dur": 4, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540606, "dur": 36, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540646, "dur": 3, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540650, "dur": 25, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540677, "dur": 2, "ph": "X", "name": "ProcessMessages 1159", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540680, "dur": 16, "ph": "X", "name": "ReadAsync 1159", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540698, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540700, "dur": 15, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540718, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540720, "dur": 26, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540749, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540752, "dur": 28, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540784, "dur": 2, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540789, "dur": 28, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540821, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540825, "dur": 34, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540865, "dur": 3, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540870, "dur": 18, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540890, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540892, "dur": 51, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540948, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885540952, "dur": 57, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541012, "dur": 3, "ph": "X", "name": "ProcessMessages 2044", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541017, "dur": 26, "ph": "X", "name": "ReadAsync 2044", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541046, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541048, "dur": 23, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541074, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541077, "dur": 30, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541110, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541113, "dur": 20, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541137, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541141, "dur": 83, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541230, "dur": 5, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541238, "dur": 51, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541295, "dur": 6, "ph": "X", "name": "ProcessMessages 1802", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541303, "dur": 37, "ph": "X", "name": "ReadAsync 1802", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541345, "dur": 3, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541349, "dur": 39, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541393, "dur": 3, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541398, "dur": 27, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541430, "dur": 3, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541435, "dur": 42, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541481, "dur": 3, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541486, "dur": 34, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541522, "dur": 2, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541526, "dur": 24, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541555, "dur": 3, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541561, "dur": 32, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541597, "dur": 6, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541605, "dur": 37, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541646, "dur": 3, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541650, "dur": 27, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541681, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541684, "dur": 25, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541714, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541717, "dur": 34, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541755, "dur": 2, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541759, "dur": 27, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541789, "dur": 2, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541792, "dur": 20, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541814, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541817, "dur": 33, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541854, "dur": 3, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541858, "dur": 32, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541894, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541898, "dur": 38, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541941, "dur": 4, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541947, "dur": 31, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541983, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885541986, "dur": 28, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542019, "dur": 3, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542024, "dur": 24, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542050, "dur": 2, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542054, "dur": 23, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542079, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542084, "dur": 20, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542106, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542109, "dur": 32, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542147, "dur": 3, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542154, "dur": 39, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542197, "dur": 3, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542202, "dur": 30, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542235, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542238, "dur": 15, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542255, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542257, "dur": 15, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542274, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542276, "dur": 19, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542297, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542300, "dur": 30, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542332, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542335, "dur": 21, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542358, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542361, "dur": 19, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542382, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542384, "dur": 13, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542401, "dur": 17, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542423, "dur": 28, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542454, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542457, "dur": 40, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542503, "dur": 4, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542510, "dur": 37, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542553, "dur": 4, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542560, "dur": 36, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542600, "dur": 3, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542605, "dur": 45, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542655, "dur": 3, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542659, "dur": 29, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542692, "dur": 3, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542697, "dur": 183, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542885, "dur": 2, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542890, "dur": 53, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542950, "dur": 8, "ph": "X", "name": "ProcessMessages 2982", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885542961, "dur": 47, "ph": "X", "name": "ReadAsync 2982", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543014, "dur": 5, "ph": "X", "name": "ProcessMessages 1405", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543021, "dur": 37, "ph": "X", "name": "ReadAsync 1405", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543063, "dur": 3, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543069, "dur": 27, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543099, "dur": 2, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543104, "dur": 32, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543150, "dur": 5, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543159, "dur": 46, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543209, "dur": 4, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543216, "dur": 37, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543256, "dur": 4, "ph": "X", "name": "ProcessMessages 1167", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543261, "dur": 31, "ph": "X", "name": "ReadAsync 1167", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543296, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543301, "dur": 44, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543351, "dur": 3, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543356, "dur": 45, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543405, "dur": 4, "ph": "X", "name": "ProcessMessages 1210", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543411, "dur": 32, "ph": "X", "name": "ReadAsync 1210", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543447, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543452, "dur": 40, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543496, "dur": 4, "ph": "X", "name": "ProcessMessages 1296", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543503, "dur": 22, "ph": "X", "name": "ReadAsync 1296", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543528, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543532, "dur": 67, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543604, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543608, "dur": 44, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543656, "dur": 4, "ph": "X", "name": "ProcessMessages 1325", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543663, "dur": 42, "ph": "X", "name": "ReadAsync 1325", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543710, "dur": 4, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543716, "dur": 27, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543746, "dur": 2, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543750, "dur": 34, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543789, "dur": 3, "ph": "X", "name": "ProcessMessages 980", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543794, "dur": 35, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543836, "dur": 2, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543840, "dur": 42, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543889, "dur": 4, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543896, "dur": 53, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543953, "dur": 4, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885543961, "dur": 47, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544014, "dur": 5, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544022, "dur": 45, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544073, "dur": 4, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544079, "dur": 43, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544128, "dur": 4, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544135, "dur": 40, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544180, "dur": 3, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544186, "dur": 30, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544222, "dur": 3, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544228, "dur": 38, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544269, "dur": 4, "ph": "X", "name": "ProcessMessages 1112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544276, "dur": 31, "ph": "X", "name": "ReadAsync 1112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544312, "dur": 2, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544316, "dur": 32, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544353, "dur": 3, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544359, "dur": 33, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544397, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544401, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544434, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544437, "dur": 27, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544467, "dur": 3, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544473, "dur": 26, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544502, "dur": 2, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544506, "dur": 48, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544560, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544565, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544603, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544607, "dur": 92, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544704, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544707, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544744, "dur": 2, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544747, "dur": 30, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544784, "dur": 3, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544789, "dur": 42, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544837, "dur": 3, "ph": "X", "name": "ProcessMessages 1091", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544841, "dur": 32, "ph": "X", "name": "ReadAsync 1091", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544879, "dur": 3, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544885, "dur": 28, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544918, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544923, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544994, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885544997, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545043, "dur": 3, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545048, "dur": 42, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545095, "dur": 3, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545101, "dur": 31, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545134, "dur": 2, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545139, "dur": 23, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545166, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545169, "dur": 28, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545202, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545208, "dur": 90, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545303, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545306, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545355, "dur": 4, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545362, "dur": 21, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885545385, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547005, "dur": 221, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547231, "dur": 29, "ph": "X", "name": "ProcessMessages 12726", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547264, "dur": 35, "ph": "X", "name": "ReadAsync 12726", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547306, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547313, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547355, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547358, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547390, "dur": 2, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547394, "dur": 25, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547421, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547424, "dur": 79, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547505, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547508, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547534, "dur": 2, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547538, "dur": 20, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547560, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547563, "dur": 24, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547593, "dur": 3, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547598, "dur": 57, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547663, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547701, "dur": 3, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547707, "dur": 30, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547741, "dur": 3, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547746, "dur": 52, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547804, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547807, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547844, "dur": 2, "ph": "X", "name": "ProcessMessages 1144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547848, "dur": 21, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547871, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547874, "dur": 44, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547923, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547927, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547973, "dur": 2, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547977, "dur": 18, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885547999, "dur": 2, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548003, "dur": 55, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548062, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548083, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548087, "dur": 26, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548115, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548117, "dur": 15, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548135, "dur": 2, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548138, "dur": 64, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548205, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548207, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548238, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548241, "dur": 22, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548268, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548272, "dur": 50, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548328, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548333, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548384, "dur": 2, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548388, "dur": 20, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548413, "dur": 3, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548418, "dur": 32, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548455, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548458, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548483, "dur": 2, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548486, "dur": 21, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548510, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548514, "dur": 24, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548541, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548544, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548568, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548570, "dur": 17, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548589, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548591, "dur": 17, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548610, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548612, "dur": 72, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548690, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548694, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548742, "dur": 4, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548749, "dur": 22, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548773, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548777, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548814, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548818, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548851, "dur": 3, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548857, "dur": 25, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548885, "dur": 2, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548890, "dur": 19, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548911, "dur": 2, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548916, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548939, "dur": 2, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548943, "dur": 19, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548967, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548972, "dur": 23, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885548998, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549002, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549045, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549049, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549096, "dur": 3, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549101, "dur": 26, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549130, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549132, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549175, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549179, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549227, "dur": 3, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549232, "dur": 29, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549263, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549266, "dur": 32, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549303, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549307, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549343, "dur": 3, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549349, "dur": 21, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549373, "dur": 2, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549377, "dur": 39, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549421, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549424, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549454, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549457, "dur": 22, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549485, "dur": 3, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549490, "dur": 22, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549515, "dur": 2, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549519, "dur": 48, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549571, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549574, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549605, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549609, "dur": 21, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549632, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549635, "dur": 20, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549659, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549662, "dur": 19, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549683, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549685, "dur": 19, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549710, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549715, "dur": 29, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549747, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549749, "dur": 52, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549806, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549809, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549831, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549834, "dur": 18, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549855, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549858, "dur": 15, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549876, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549878, "dur": 49, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549932, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549955, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549957, "dur": 18, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549978, "dur": 2, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885549982, "dur": 28, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550015, "dur": 3, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550020, "dur": 35, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550058, "dur": 2, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550062, "dur": 21, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550086, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550089, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550105, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550108, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550163, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550187, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550189, "dur": 16, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550207, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550209, "dur": 14, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550225, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550227, "dur": 57, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550288, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550310, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550313, "dur": 32, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550349, "dur": 2, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550353, "dur": 21, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550376, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550379, "dur": 17, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550398, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550401, "dur": 20, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550423, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550426, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550446, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550449, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550503, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550527, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550530, "dur": 19, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550552, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550555, "dur": 50, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550610, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550632, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550635, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550657, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550660, "dur": 54, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550717, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550719, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550748, "dur": 2, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550752, "dur": 18, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550772, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550807, "dur": 24, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550833, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550836, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550867, "dur": 3, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550873, "dur": 37, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550914, "dur": 2, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550918, "dur": 32, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550955, "dur": 3, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885550960, "dur": 39, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551004, "dur": 3, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551010, "dur": 28, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551041, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551045, "dur": 22, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551071, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551074, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551115, "dur": 4, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551122, "dur": 25, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551149, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551153, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551202, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551206, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551244, "dur": 3, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551250, "dur": 17, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551269, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551273, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551336, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551339, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551362, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551369, "dur": 28, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551400, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551403, "dur": 48, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551456, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551459, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551485, "dur": 2, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551488, "dur": 19, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551512, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551517, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551563, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551566, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551593, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551596, "dur": 28, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551627, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551629, "dur": 35, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551667, "dur": 2, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551671, "dur": 24, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551700, "dur": 3, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551706, "dur": 27, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551738, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551743, "dur": 56, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551804, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551832, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551835, "dur": 28, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551869, "dur": 3, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551875, "dur": 63, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551943, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551947, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551991, "dur": 3, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885551996, "dur": 15, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552013, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552016, "dur": 55, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552075, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552078, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552106, "dur": 2, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552110, "dur": 29, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552143, "dur": 2, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552147, "dur": 23, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552172, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552175, "dur": 56, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552240, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552272, "dur": 3, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552278, "dur": 50, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552336, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552355, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552357, "dur": 50, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552412, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552434, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552437, "dur": 18, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552457, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552460, "dur": 16, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552479, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552481, "dur": 56, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552542, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552547, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552579, "dur": 3, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552584, "dur": 26, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552615, "dur": 2, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552619, "dur": 48, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552671, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552700, "dur": 4, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552706, "dur": 29, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552739, "dur": 2, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552743, "dur": 48, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552796, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552819, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552822, "dur": 24, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552849, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552851, "dur": 23, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552879, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552883, "dur": 19, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552904, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552906, "dur": 15, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552924, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552927, "dur": 16, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552947, "dur": 14, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552964, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885552966, "dur": 83, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553055, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553059, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553106, "dur": 4, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553113, "dur": 32, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553149, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553153, "dur": 95, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553257, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553261, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553307, "dur": 3, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553312, "dur": 34, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553350, "dur": 2, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553354, "dur": 58, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553421, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553424, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553458, "dur": 2, "ph": "X", "name": "ProcessMessages 1107", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553462, "dur": 22, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553490, "dur": 3, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553495, "dur": 52, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553552, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553557, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553605, "dur": 4, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553612, "dur": 26, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553643, "dur": 2, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553648, "dur": 36, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553690, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553694, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553735, "dur": 4, "ph": "X", "name": "ProcessMessages 1237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553741, "dur": 25, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553769, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553773, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553825, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553830, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553865, "dur": 3, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553870, "dur": 24, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553898, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553903, "dur": 58, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885553969, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554007, "dur": 3, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554011, "dur": 23, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554037, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554040, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554092, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554115, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554117, "dur": 22, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554145, "dur": 2, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554149, "dur": 16, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554167, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554169, "dur": 45, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554219, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554246, "dur": 3, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554251, "dur": 25, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554279, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554284, "dur": 37, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554323, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554325, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554347, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554350, "dur": 20, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554373, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554376, "dur": 14, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554392, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554395, "dur": 47, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554446, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554468, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554471, "dur": 24, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554500, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554505, "dur": 43, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554552, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554578, "dur": 2, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554581, "dur": 19, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554602, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554605, "dur": 51, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554661, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554681, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554684, "dur": 20, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554707, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554710, "dur": 15, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554728, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554731, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554752, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554754, "dur": 23, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554781, "dur": 2, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554785, "dur": 21, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554808, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554810, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554861, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554864, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554890, "dur": 2, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554894, "dur": 19, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554916, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554920, "dur": 51, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554975, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554993, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885554996, "dur": 19, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555017, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555019, "dur": 15, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555037, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555039, "dur": 45, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555087, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555090, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555112, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555115, "dur": 31, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555149, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555152, "dur": 48, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555205, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555227, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555231, "dur": 21, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555254, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555257, "dur": 47, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555308, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555330, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555333, "dur": 19, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555354, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555357, "dur": 18, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555377, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555380, "dur": 18, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555400, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555403, "dur": 20, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555425, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555428, "dur": 19, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555449, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555452, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555520, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555546, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555549, "dur": 20, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555571, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555574, "dur": 59, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555637, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555665, "dur": 2, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555669, "dur": 19, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555690, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555693, "dur": 45, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555743, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555765, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555768, "dur": 20, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555790, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555793, "dur": 51, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555848, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555870, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555873, "dur": 30, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555907, "dur": 3, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555912, "dur": 15, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555929, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885555939, "dur": 62, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556006, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556032, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556036, "dur": 23, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556060, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556063, "dur": 15, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556081, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556083, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556135, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556155, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556158, "dur": 16, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556176, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556179, "dur": 53, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556234, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556236, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556258, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556260, "dur": 23, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556285, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556289, "dur": 46, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556340, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556359, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556362, "dur": 19, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556384, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556386, "dur": 13, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556402, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556405, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556462, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556482, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556485, "dur": 32, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556518, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556521, "dur": 21, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556545, "dur": 2, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556548, "dur": 25, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556578, "dur": 2, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556582, "dur": 15, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556599, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556601, "dur": 14, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556617, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556620, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556694, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556721, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556724, "dur": 20, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556748, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556752, "dur": 68, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556827, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556850, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556853, "dur": 17, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556872, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885556875, "dur": 932, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885557815, "dur": 5, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885557822, "dur": 104, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885557931, "dur": 330, "ph": "X", "name": "ProcessMessages 7115", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558266, "dur": 290, "ph": "X", "name": "ReadAsync 7115", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558562, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558567, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558604, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558609, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558642, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558647, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558678, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558682, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558707, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558712, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558737, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558741, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558767, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558772, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558796, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558800, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558828, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558833, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558857, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558862, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558886, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558891, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558917, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558922, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558957, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558965, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558993, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885558998, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559038, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559043, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559071, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559077, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559103, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559109, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559133, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559138, "dur": 22, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559165, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559172, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559199, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559204, "dur": 21, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559229, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559235, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559277, "dur": 8, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559288, "dur": 29, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559322, "dur": 7, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559332, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559373, "dur": 6, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559383, "dur": 28, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559414, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559419, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559453, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559488, "dur": 5, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559495, "dur": 35, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559534, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559542, "dur": 30, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559577, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559583, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559612, "dur": 4, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559619, "dur": 29, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559652, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559660, "dur": 35, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559699, "dur": 6, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559708, "dur": 30, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559742, "dur": 5, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559749, "dur": 30, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559784, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559792, "dur": 34, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559831, "dur": 6, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559839, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559891, "dur": 7, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559900, "dur": 38, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559943, "dur": 5, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559951, "dur": 34, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885559990, "dur": 7, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560000, "dur": 41, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560046, "dur": 6, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560055, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560091, "dur": 5, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560098, "dur": 30, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560133, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560139, "dur": 28, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560171, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560179, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560227, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560234, "dur": 36, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560276, "dur": 6, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560285, "dur": 43, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560333, "dur": 5, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560340, "dur": 37, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560382, "dur": 9, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560394, "dur": 40, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560439, "dur": 6, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560448, "dur": 30, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560483, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560491, "dur": 35, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560531, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560538, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560577, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560585, "dur": 57, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560647, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560655, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560694, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560702, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560737, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560742, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560779, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560787, "dur": 34, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560826, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560833, "dur": 34, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560872, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560880, "dur": 31, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560915, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560922, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560961, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885560967, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561008, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561015, "dur": 30, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561050, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561057, "dur": 30, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561092, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561099, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561134, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561141, "dur": 35, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561181, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561187, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561221, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561228, "dur": 26, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561259, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561265, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561290, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561294, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561324, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561328, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561361, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561366, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561393, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885561397, "dur": 8986, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885570397, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885570406, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885570450, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885570456, "dur": 2358, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885572828, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885572837, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885572882, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885572888, "dur": 8690, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885581591, "dur": 22, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885581621, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885581666, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885581670, "dur": 17, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885581689, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885581691, "dur": 565, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582262, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582267, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582309, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582314, "dur": 181, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582500, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582504, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582528, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582538, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582564, "dur": 9, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582577, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582625, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582628, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582645, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582648, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582688, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885582692, "dur": 393, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583090, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583095, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583120, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583124, "dur": 15, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583141, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583145, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583194, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583198, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583217, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583221, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583244, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583251, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583268, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583271, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583295, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583301, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583320, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583325, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583345, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583348, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583364, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583367, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583497, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583501, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583519, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583522, "dur": 92, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583620, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583624, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583647, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583651, "dur": 102, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583758, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583762, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583783, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583787, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583803, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583806, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583832, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583837, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583898, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583903, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583923, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583926, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583984, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885583989, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584010, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584014, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584036, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584039, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584059, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584063, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584084, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584087, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584103, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584109, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584130, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584134, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584184, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584200, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584203, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584261, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584266, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584295, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584300, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584319, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584322, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584339, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584342, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584365, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584369, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584394, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584398, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584450, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584454, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584489, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584493, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584511, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584515, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584594, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584615, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584618, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584661, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584680, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584683, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584697, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584700, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584720, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584724, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584748, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584752, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584767, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584770, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584836, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584840, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584859, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885584863, "dur": 206, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585073, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585077, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585096, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585099, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585113, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585115, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585186, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585189, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585207, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585210, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585223, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585225, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585291, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585295, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585315, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585319, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585349, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585353, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585373, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585376, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585391, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585393, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585455, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585458, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585482, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585487, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585507, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585510, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585571, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585575, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585592, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585595, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585670, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585689, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585692, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585777, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585781, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585801, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585804, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585818, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585821, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585902, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585905, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585925, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585929, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585957, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585961, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585989, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885585992, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586027, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586031, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586132, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586136, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586154, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586158, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586183, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586189, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586210, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586219, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586240, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586244, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586270, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586275, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586294, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586299, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586340, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586343, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586379, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586384, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586480, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586506, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586510, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586525, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586528, "dur": 14, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586548, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586552, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586612, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586615, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586629, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586632, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586704, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586710, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586726, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586729, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586756, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586769, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885586773, "dur": 3216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885590002, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885590011, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885590126, "dur": 749, "ph": "X", "name": "ProcessMessages 1368", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885590882, "dur": 45, "ph": "X", "name": "ReadAsync 1368", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885590933, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885590938, "dur": 28, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885590972, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885590978, "dur": 273, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591254, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591257, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591303, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591310, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591432, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591438, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591467, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591472, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591495, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591498, "dur": 410, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591915, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591921, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591960, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591965, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591987, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885591990, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592035, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592038, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592072, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592076, "dur": 481, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592564, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592569, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592605, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592609, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592674, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592677, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592705, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592709, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592736, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592739, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592765, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885592769, "dur": 232, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593007, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593011, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593044, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593049, "dur": 138, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593194, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593198, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593221, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593225, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593275, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593278, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593300, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593304, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593518, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593522, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593553, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593557, "dur": 297, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593859, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593862, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593896, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885593899, "dur": 365, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885594269, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885594272, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885594289, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885594292, "dur": 278659, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885872967, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885872977, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885873022, "dur": 2003, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885875037, "dur": 6652, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881704, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881714, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881749, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881754, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881771, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881774, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881789, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881792, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881819, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881825, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881990, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885881995, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882022, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882025, "dur": 129, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882160, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882163, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882183, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882186, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882268, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882272, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882293, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882296, "dur": 396, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882697, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882701, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882719, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885882723, "dur": 1167, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885883897, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885883902, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885883939, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885883944, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884089, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884093, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884117, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884120, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884158, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884179, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884182, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884202, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884217, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884220, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884407, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884410, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884435, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884438, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884454, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884457, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884480, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884485, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884711, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884714, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884739, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884742, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884802, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884805, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884827, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885884831, "dur": 412, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885246, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885249, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885264, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885267, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885375, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885378, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885392, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885395, "dur": 342, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885740, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885743, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885763, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885885767, "dur": 339, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886110, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886113, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886129, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886132, "dur": 513, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886649, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886653, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886670, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886673, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886753, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886769, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886772, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886880, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886896, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886899, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886949, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886969, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885886972, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887010, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887013, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887027, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887030, "dur": 442, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887475, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887478, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887495, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887497, "dur": 318, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887820, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887824, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887848, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885887851, "dur": 413, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885888268, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885888271, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885888287, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885888291, "dur": 313, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885888608, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885888612, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885888641, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885888645, "dur": 543, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889193, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889197, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889227, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889232, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889295, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889298, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889324, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889329, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889365, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889380, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889384, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889540, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889545, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889568, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889573, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889679, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889682, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889697, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889700, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889747, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889750, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889770, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889774, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889862, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889865, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889884, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885889888, "dur": 341, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890234, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890239, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890277, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890282, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890330, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890351, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890355, "dur": 349, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890708, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890711, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890726, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890729, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890921, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890924, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890947, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885890951, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891035, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891050, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891052, "dur": 346, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891404, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891407, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891420, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891423, "dur": 286, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891717, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891720, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891739, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891743, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891884, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891887, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891906, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891921, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885891924, "dur": 233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892160, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892162, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892178, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892181, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892202, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892207, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892284, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892287, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892307, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892311, "dur": 380, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892694, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892697, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892713, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892716, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892779, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892795, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885892798, "dur": 2999, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885895804, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885895808, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885895909, "dur": 41, "ph": "X", "name": "ProcessMessages 2624", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885895953, "dur": 33, "ph": "X", "name": "ReadAsync 2624", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885895990, "dur": 5, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885895998, "dur": 28, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896030, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896034, "dur": 182, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896223, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896227, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896260, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896265, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896331, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896334, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896363, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896367, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896445, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896468, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896472, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896495, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896499, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896634, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896639, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896685, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896692, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896724, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896728, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896829, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885896835, "dur": 55655, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885952503, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885952511, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885952535, "dur": 318, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 12116, "tid": 12884901888, "ts": 1751465885952859, "dur": 29548, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 12116, "tid": 12, "ts": 1751465885999925, "dur": 5236, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 12116, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 12116, "tid": 8589934592, "ts": 1751465885491766, "dur": 455495, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 12116, "tid": 8589934592, "ts": 1751465885947264, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 12116, "tid": 8589934592, "ts": 1751465885947274, "dur": 1073, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 12116, "tid": 12, "ts": 1751465886005165, "dur": 28, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 12116, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 12116, "tid": 4294967296, "ts": 1751465885464515, "dur": 519376, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 12116, "tid": 4294967296, "ts": 1751465885469826, "dur": 15939, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 12116, "tid": 4294967296, "ts": 1751465885984229, "dur": 7434, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 12116, "tid": 4294967296, "ts": 1751465885987424, "dur": 2422, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 12116, "tid": 4294967296, "ts": 1751465885991762, "dur": 25, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 12116, "tid": 12, "ts": 1751465886005196, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751465885498119, "dur": 33665, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751465885531793, "dur": 1797, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751465885533710, "dur": 144, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751465885533855, "dur": 330, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751465885534310, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_878D99D4DAFDE3CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751465885535117, "dur": 363, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4EAA64F102651546.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751465885549032, "dur": 180, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14507123614329245381.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751465885534202, "dur": 25292, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751465885559513, "dur": 394475, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751465885953989, "dur": 186, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751465885954176, "dur": 75, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751465885954446, "dur": 24478, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751465885534390, "dur": 25129, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885559585, "dur": 983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751465885560599, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751465885560818, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C11B61CB0C885270.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751465885560943, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751465885561083, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885561511, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751465885561662, "dur": 10613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751465885572379, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885572817, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885573424, "dur": 518, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\Analytics\\RenderPipelineGraphicsSettingsAnalytics.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751465885573180, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885574053, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885574745, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885575749, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\EvaluationVisitor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751465885575651, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885576736, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885577711, "dur": 604, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\PostProcessing\\LensFlareOcclusionPermutation.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751465885577315, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885578315, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885578779, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885579172, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885579695, "dur": 2871, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Plugins\\XR\\XRLayoutBuilder.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751465885579494, "dur": 3172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885582666, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885583553, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885584302, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751465885584612, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751465885585312, "dur": 1029, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885586367, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751465885586600, "dur": 1840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751465885588441, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885588608, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885588744, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751465885589286, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885589375, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885590449, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751465885590581, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751465885590986, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885591405, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885592191, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751465885592351, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751465885592826, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885593331, "dur": 1693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885595024, "dur": 286408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885881433, "dur": 2216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751465885883693, "dur": 5185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751465885888879, "dur": 922, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885889808, "dur": 2852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751465885892697, "dur": 3021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751465885895719, "dur": 1203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885896930, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885897120, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885897267, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885897489, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885897758, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751465885897827, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751465885897974, "dur": 56028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885534438, "dur": 25103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885559543, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751465885560610, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885560820, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_33DE0D3A006A6424.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751465885562013, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751465885562236, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751465885562599, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885562760, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751465885563207, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885563416, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885563605, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885565456, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-utility-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751465885564960, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885566558, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885567361, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885567836, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885568356, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885569393, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885569649, "dur": 772, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\SequenceContext.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751465885569649, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885570813, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885571620, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885572698, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885572889, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\Volume\\SerializedDataParameter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751465885573417, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\Volume\\Drawers\\TextureParameterDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751465885574049, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\Volume\\Drawers\\FloatParameterDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751465885574725, "dur": 698, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\Utilities\\SerializedBitArray.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751465885572889, "dur": 2725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885575614, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885576222, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885576640, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885577434, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885577794, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885578426, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885578731, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885579154, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885579345, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885581228, "dur": 3038, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Editor\\UITKAssetEditor\\Views\\DropManipulator.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751465885579851, "dur": 4620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885584472, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751465885584676, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885585614, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885585782, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751465885585935, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885586028, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885586828, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885587323, "dur": 909, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885588264, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751465885588460, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885589069, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885589651, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751465885589802, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885590281, "dur": 1133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885591414, "dur": 3604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885595018, "dur": 286428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885881448, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885883715, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885883776, "dur": 2579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885886394, "dur": 2249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885888644, "dur": 813, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885889464, "dur": 2805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885892312, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885894591, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885895225, "dur": 2196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751465885897590, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751465885897824, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751465885897944, "dur": 56045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885534480, "dur": 25078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885559560, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885560632, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885560714, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_07ED47E0EFE03F3D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885560787, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_EB030C3D9E9F35E6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885560927, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885561454, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751465885561720, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751465885561778, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751465885562233, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751465885562506, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751465885562632, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885563127, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885563305, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885563775, "dur": 2141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885565916, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885566279, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885566649, "dur": 1176, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Editor\\Lighting\\UniversalRenderPipelineLightUI.Drawers.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751465885566495, "dur": 1698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885568194, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885569068, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885569494, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885569958, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885570518, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885571095, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885571935, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885573046, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885573810, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885574238, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885574782, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885575367, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885575874, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885576822, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885577267, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885578473, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerObject.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751465885577623, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885579027, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885579355, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885580225, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885580591, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885580814, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885581050, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885581305, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885581455, "dur": 1283, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@0a21eb82d95c\\UnityEditor.TestRunner\\TestRun\\Tasks\\CreateBootstrapSceneTask.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751465885581454, "dur": 1364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885582819, "dur": 725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885583571, "dur": 725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885584299, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885584482, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885585943, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885586118, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885586386, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885587102, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885587273, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885587554, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885588644, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885588855, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885589037, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885589584, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885589748, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885590446, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885590557, "dur": 1331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885591889, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885591966, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885592076, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885592173, "dur": 1674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885593898, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885594016, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885594557, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885594656, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885595014, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751465885595176, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885595501, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885595838, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885596248, "dur": 285189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885881443, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885883748, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885886068, "dur": 3772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885889841, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885890259, "dur": 2339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885892599, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885893025, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885895484, "dur": 2399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751465885897932, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751465885898720, "dur": 55272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885534410, "dur": 25118, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885559589, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751465885560630, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885560815, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F4BBF2BF2203F2D5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751465885561050, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D5FB1AB832332DD5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751465885561206, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_ACB0EC32CF196063.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751465885561414, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751465885561684, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751465885561830, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751465885562294, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751465885562472, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751465885562588, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885562759, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751465885562855, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751465885563137, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7908171937365489455.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751465885563226, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885563436, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885565255, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885565449, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885565807, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885566473, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885567020, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885567773, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885568377, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885569247, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885569518, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885570505, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885571260, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885572217, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Nodes\\Math\\Advanced\\ReciprocalNode.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751465885571790, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885572778, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885573170, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885573553, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885574074, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885575170, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\MultiInputUnit.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751465885574674, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885576009, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885576462, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885577163, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885577507, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885577906, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885578374, "dur": 659, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\DivisionHandler.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751465885578137, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885579138, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885579348, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885579763, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885580337, "dur": 627, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Editor\\AssetEditor\\InputActionEditorToolbar.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751465885580092, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885581102, "dur": 2745, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\uint3.gen.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751465885581079, "dur": 2962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885584041, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885584315, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751465885584542, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751465885584687, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751465885585124, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751465885585264, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751465885585864, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751465885586009, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885586378, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751465885587167, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751465885587345, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751465885587858, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885587941, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751465885589044, "dur": 203, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885589416, "dur": 285490, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751465885881433, "dur": 2202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751465885883684, "dur": 2922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751465885886607, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885886792, "dur": 2105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751465885888935, "dur": 2104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751465885891040, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885891517, "dur": 1963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751465885893481, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885893875, "dur": 2156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751465885896032, "dur": 645, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885896765, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885896982, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885897756, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751465885897840, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751465885897951, "dur": 56039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885534425, "dur": 25110, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885559538, "dur": 1055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751465885560616, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_24CD4430EA38EE97.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751465885560823, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_5C2FAC469D562E01.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751465885561034, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_099E8E7D5E4735AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751465885561415, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751465885561798, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751465885561968, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751465885562240, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751465885562471, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751465885562588, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885562755, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751465885563199, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5051408519606676264.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751465885563313, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885563734, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885565167, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885565300, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885565513, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885565963, "dur": 642, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f502d8ac613f\\Editor\\Analytics\\MultiplayerCenterAnalyticsFactory.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751465885566632, "dur": 1113, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f502d8ac613f\\Editor\\Analytics\\DebugAnalytics.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751465885565853, "dur": 3025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885568878, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885570800, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Sequence\\Jog.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751465885570105, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885571740, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885573418, "dur": 1194, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.State\\States\\AnyStateDescriptor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751465885572780, "dur": 1832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885574676, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\ScriptGraphAsset.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751465885574612, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885575701, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885576813, "dur": 779, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\UlongInspector.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751465885576455, "dur": 1710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885578165, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885578980, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885579654, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885580293, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885580547, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885580895, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885581149, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885581443, "dur": 2699, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@0a21eb82d95c\\UnityEditor.TestRunner\\TestRunner\\Messages\\EnterPlayMode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751465885581344, "dur": 2804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885584149, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885584307, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751465885584485, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751465885585110, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751465885585689, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885585809, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751465885586024, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751465885586167, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751465885586349, "dur": 1173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751465885587580, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885587765, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751465885587976, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751465885588574, "dur": 1040, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885589639, "dur": 1774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885591413, "dur": 3614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885595027, "dur": 286403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885881433, "dur": 2301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751465885883785, "dur": 2617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751465885886439, "dur": 2160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751465885888638, "dur": 3045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751465885891684, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885892211, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751465885892267, "dur": 4922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751465885897190, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885897285, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885897583, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885897721, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885897913, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751465885898456, "dur": 55540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885534447, "dur": 25100, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885559549, "dur": 1046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751465885560596, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885560897, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885561018, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_847E2D6319A6BEA0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751465885561624, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751465885562148, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751465885562297, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751465885562573, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885562758, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751465885563123, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885563363, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885563595, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885565333, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885565696, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885565883, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885566065, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885566237, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885566426, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885566923, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885567521, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885567960, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885569246, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885569493, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885569677, "dur": 810, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\TimelineTreeView.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751465885569677, "dur": 2049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885571727, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885572692, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885573101, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885573999, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885574485, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885575033, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885575449, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885575988, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885576811, "dur": 1021, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Graph\\LudiqGraphsEditorUtility.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751465885576479, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885578356, "dur": 1036, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Utilities\\Recursion.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751465885577940, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885579600, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885580209, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885580771, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885581015, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885581240, "dur": 3435, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Editor\\UGUI\\UI\\InterceptedEventsPreview.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751465885581240, "dur": 3446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885584687, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885585226, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885585766, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751465885585970, "dur": 1175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885587145, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885587204, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751465885587369, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885587653, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885588311, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885588519, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751465885588692, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885589157, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885589592, "dur": 1839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885591431, "dur": 3588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885595019, "dur": 286424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885881444, "dur": 3190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885884682, "dur": 1968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885886702, "dur": 1940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885888644, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885889001, "dur": 2249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885891251, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885891352, "dur": 2539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885893892, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885894170, "dur": 1831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885896040, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751465885898203, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751465885898309, "dur": 55675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885534469, "dur": 25083, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885559555, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751465885560613, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885560796, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_C0823A9ADF385DE2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751465885560960, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885561016, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751465885561786, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751465885562016, "dur": 300, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751465885562554, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751465885562629, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885563122, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885563186, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10266345212867571173.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751465885563276, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885564498, "dur": 854, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Ini.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751465885565446, "dur": 1851, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751465885563722, "dur": 4159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885567957, "dur": 908, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Deprecated\\CylinderVolumeDeprecated.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751465885567881, "dur": 2138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885570020, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885570881, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\ShaderGUI\\GenericShaderGraphMaterialGUI.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751465885570484, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885571441, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885572166, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885573416, "dur": 895, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Runtime\\UniversalRendererDebug.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751465885573246, "dur": 1769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885575016, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885575385, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885575901, "dur": 602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Utilities\\SearchUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751465885576814, "dur": 843, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Utilities\\PackageVersionUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751465885575852, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885578363, "dur": 790, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b\\Path\\Editor\\IMGUI\\GUIFramework\\GenericDefaultControl.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751465885577800, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885579295, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885580033, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Editor\\InputAssetEditorUtils.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751465885581241, "dur": 1118, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Editor\\AssetImporter\\InputActionImporter.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751465885580012, "dur": 2348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885582477, "dur": 1070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885583547, "dur": 790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885584338, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751465885584567, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885585048, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885585182, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_FD54DBCE44D88AE4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751465885585336, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751465885585537, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885586157, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885586364, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885586581, "dur": 715, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885587301, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885587890, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885588689, "dur": 627, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885589377, "dur": 1293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885590671, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751465885590808, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885591210, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885591293, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885591708, "dur": 3308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885595019, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751465885595258, "dur": 286191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885881451, "dur": 2475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885883968, "dur": 1993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885885962, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885886101, "dur": 2174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885888276, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885888733, "dur": 2504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885891279, "dur": 2079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885893392, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885895620, "dur": 699, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751465885896324, "dur": 2073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751465885898438, "dur": 55556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885534488, "dur": 25076, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885559566, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885560601, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_385F174668EB898F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885560890, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_767627B5731476EF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885561420, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751465885561575, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885561659, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751465885561962, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751465885562074, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751465885562246, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751465885562351, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751465885562584, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885563224, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885563464, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885565100, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885566651, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885567055, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885567585, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885568267, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885569121, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885569360, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885569572, "dur": 811, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Runtime\\2D\\Rendergraph\\DrawLight2DPass.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751465885569572, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885570719, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885571226, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885572222, "dur": 731, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Legacy\\SpriteUnlitMasterNode1.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751465885572154, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885573427, "dur": 921, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Runtime\\ShadowCulling.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751465885573265, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885574812, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885575248, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885575907, "dur": 914, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\SerializationVisitor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751465885576821, "dur": 1133, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\ParameterArgs.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751465885575632, "dur": 2412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885578349, "dur": 1447, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticFunctionInvokerBase.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751465885578044, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885579915, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885580262, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885580821, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885581109, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885581373, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885581809, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885582026, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885582478, "dur": 1071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885583550, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885584304, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885584522, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885585183, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885585532, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885585746, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885586581, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885586750, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885587372, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885588002, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885588172, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885588259, "dur": 581, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885588870, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885589207, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885589753, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885589910, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885590668, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885590846, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885591401, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885591577, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885592181, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885592264, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751465885592425, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885592937, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885593275, "dur": 1756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885595031, "dur": 286408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885881440, "dur": 2424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885883865, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885884256, "dur": 2941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885887231, "dur": 2658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885889890, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885890276, "dur": 2723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885893000, "dur": 695, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751465885893702, "dur": 2767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885896509, "dur": 2072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751465885898618, "dur": 55378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885534505, "dur": 25064, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885559572, "dur": 1096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751465885560743, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_A1C14A714FD44069.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751465885560957, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751465885561415, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751465885561717, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751465885562009, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751465885562560, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751465885562615, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885563218, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885564978, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Xml.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751465885563428, "dur": 2782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885566211, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885566491, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885567586, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885568905, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885569427, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885569625, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885570180, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Items\\MarkerItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751465885570907, "dur": 860, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Items\\ItemsPerTrack.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751465885570127, "dur": 1934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885572208, "dur": 1101, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Nodes\\Artistic\\Mask\\ChannelMaskNode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751465885572062, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885573658, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885574015, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885574412, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885575239, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885575883, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885576412, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885576820, "dur": 907, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Canvases\\CanvasControlScheme.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751465885576780, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885578432, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885579269, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885579978, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885580341, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885580681, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885580871, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885581111, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885581442, "dur": 5192, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@0a21eb82d95c\\UnityEditor.TestRunner\\TestSettings\\ITestSettings.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751465885581331, "dur": 5314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885586646, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751465885586905, "dur": 1937, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885588846, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751465885589336, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885589421, "dur": 1982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885591411, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751465885591541, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751465885591881, "dur": 3151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885595032, "dur": 286412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885881447, "dur": 3124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751465885884573, "dur": 1561, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885886144, "dur": 2635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751465885888779, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885888954, "dur": 2652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751465885891607, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885891735, "dur": 2907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751465885894683, "dur": 2936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751465885897693, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751465885897880, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/spine-unity.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751465885897971, "dur": 56016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885534522, "dur": 25054, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885559579, "dur": 1019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885560634, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AB9848F6D5CE3BFE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885560957, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885561055, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885561249, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4C3484A57A05DA64.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885561510, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885561858, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885561989, "dur": 12068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885574139, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885574284, "dur": 9211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885583568, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885583665, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885584296, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885584488, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885585102, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885585296, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885585463, "dur": 873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885586376, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885586675, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885586849, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885587285, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885587445, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885587932, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885588199, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885588503, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885588696, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885589161, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885589325, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885589387, "dur": 2022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885591410, "dur": 3152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885594563, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751465885594724, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885595175, "dur": 286267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885881442, "dur": 2783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885884226, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885884687, "dur": 4158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885888872, "dur": 3300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885892173, "dur": 733, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885892913, "dur": 2157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885895071, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751465885895482, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885897835, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751465885897972, "dur": 56013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885534554, "dur": 25032, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885559588, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751465885560751, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_A5FFABD953007C60.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751465885560940, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751465885561046, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_B9FBC03C59DCE731.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751465885561703, "dur": 331, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751465885562151, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751465885562238, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751465885562416, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751465885562585, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885562798, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751465885563045, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13226667538729057029.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751465885563208, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885564718, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751465885563420, "dur": 2181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885565601, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885565840, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885566280, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885566579, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885567647, "dur": 1202, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Slots\\Implementations\\VFXSlotFloat2.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751465885567108, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885569330, "dur": 719, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Core\\VFXEnums.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751465885568937, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885570179, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885570653, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885571603, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885572343, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885573012, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885573617, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885574028, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885574422, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885575197, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885575729, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885576854, "dur": 1038, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Platforms\\MethodInfoStubWriter.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751465885576203, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885578358, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceFunctionInvoker_3.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751465885578045, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885579165, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885579354, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885580185, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885580703, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885580911, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885581452, "dur": 908, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Editor\\UGUI\\UI\\PropertyDrawers\\DropdownOptionListDrawer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751465885581192, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885582576, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885583573, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885584308, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751465885584494, "dur": 1161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751465885585656, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885585821, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751465885586026, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751465885586728, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885587351, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885587470, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751465885588127, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885588276, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751465885588433, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885588490, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751465885588695, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751465885589232, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885589372, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885589644, "dur": 1767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885591411, "dur": 3609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885595021, "dur": 286414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885881436, "dur": 6241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751465885887731, "dur": 3876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751465885891608, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885891847, "dur": 2257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751465885894108, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885894277, "dur": 2395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751465885896673, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885896969, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885897206, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885897674, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885897818, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751465885897921, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751465885898631, "dur": 55369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885534573, "dur": 25018, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885559594, "dur": 924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751465885560611, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_24AD401155842AFB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751465885560959, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_34D7E2BEC34818D5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751465885561857, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751465885562012, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751465885562155, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751465885562532, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885562800, "dur": 251, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751465885563130, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885563230, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885563494, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885564872, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885566641, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Editor\\ShaderGUI\\Shaders\\ParticlesSimpleLitShader.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751465885566466, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885567472, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885567907, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885569048, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885569833, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885570209, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885570858, "dur": 862, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\Targets\\BuiltInLitSubTarget.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751465885570604, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885571860, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885572542, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885572940, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885573345, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885573742, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885574194, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885574937, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885575433, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885575916, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885576908, "dur": 727, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyOptionTreeExtensionAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751465885576329, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885577711, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885578043, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885578778, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885579256, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885580202, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885580661, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885580855, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885581079, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885581457, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@0a21eb82d95c\\UnityEditor.TestRunner\\UnityTestProtocol\\Messages\\TestFinishedMessage.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751465885581328, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885582256, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885582560, "dur": 988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885583549, "dur": 755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885584305, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751465885584492, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751465885585102, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885585227, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751465885585487, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885585573, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751465885586387, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885586492, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751465885586639, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885586726, "dur": 2776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751465885589544, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885589636, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751465885589794, "dur": 981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751465885590776, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885590887, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751465885591033, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751465885591461, "dur": 3567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885595028, "dur": 286424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885881454, "dur": 2505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751465885883998, "dur": 2152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751465885886192, "dur": 4165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751465885890358, "dur": 809, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885891198, "dur": 3506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751465885894706, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885894782, "dur": 2100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751465885896930, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885897363, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885897628, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751465885897848, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751465885897958, "dur": 56033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885534588, "dur": 25012, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885559603, "dur": 1022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751465885560952, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751465885561717, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751465885561917, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751465885562200, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751465885562412, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751465885562482, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751465885562591, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885562759, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751465885563073, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885563223, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885563648, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885565813, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-datetime-l1-1-0.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751465885565143, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885567122, "dur": 876, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Editor\\2D\\ShapeEditor\\EditablePath\\BezierUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751465885566714, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885568063, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885569041, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885569464, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885569659, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885570856, "dur": 575, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Generation\\ITargetProvider.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751465885570677, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885571865, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885572792, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885573175, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885574010, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885574421, "dur": 1467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885575889, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885576572, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885577018, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885577309, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885577727, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885578077, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885578824, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885579643, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885580336, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885580559, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885580809, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885581242, "dur": 1602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\double2x4.gen.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751465885581105, "dur": 1899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885583005, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885583552, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885584324, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751465885584485, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885584543, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751465885584779, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885585108, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751465885585824, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885586297, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751465885586703, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751465885587667, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885587926, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751465885588112, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751465885588338, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751465885589315, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751465885589487, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751465885590034, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885590105, "dur": 1312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885591417, "dur": 3597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885595015, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751465885595209, "dur": 287043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885882253, "dur": 3580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751465885885871, "dur": 2952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751465885888867, "dur": 3267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751465885892135, "dur": 2010, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751465885894165, "dur": 4487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751465885898701, "dur": 55292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885534604, "dur": 25002, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885559608, "dur": 1229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751465885560933, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885561029, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B72398FA1A1CE63D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751465885561127, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_4AAF7BB67CBB0CA5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751465885562073, "dur": 418, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751465885562504, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751465885562628, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885562845, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751465885562943, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4730270780318326332.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751465885563025, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885563216, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885563417, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885564837, "dur": 701, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751465885564427, "dur": 2217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885566645, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885567769, "dur": 1071, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Slots\\Implementations\\VFXSlotTransform.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751465885567095, "dur": 1781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885568946, "dur": 575, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\GraphView\\Elements\\Controllers\\VFXBlockController.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751465885569521, "dur": 851, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\GraphView\\Elements\\3D\\Rotate3DManipulator.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751465885568876, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885570724, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885571171, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885571688, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885572496, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885573004, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885573734, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885574750, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Runtime\\Utilities\\EventBinding\\Implementation\\VFXTriggerEventBinder.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751465885574264, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885575604, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885576412, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885577243, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885577659, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885578148, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885579057, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885579410, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885580253, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885580619, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885580850, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885581451, "dur": 1959, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\float3x3.gen.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751465885581087, "dur": 2383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885583470, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885583554, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885584299, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751465885584480, "dur": 1746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885586226, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885586290, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885586478, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751465885586645, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885587304, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885587802, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751465885588125, "dur": 1208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885589337, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885589808, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885589891, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751465885590047, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885590442, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885590888, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885591407, "dur": 995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885592404, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751465885592586, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885593317, "dur": 1716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885595033, "dur": 286400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885881441, "dur": 2136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885883638, "dur": 2599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885886238, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885886458, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885888640, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885890971, "dur": 687, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885891663, "dur": 2491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885894181, "dur": 2158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751465885896340, "dur": 844, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885897435, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885897707, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885897805, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885897936, "dur": 52401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751465885952781, "dur": 191, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 14, "ts": 1751465885952972, "dur": 941, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 14, "ts": 1751465885950339, "dur": 3626, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885534618, "dur": 25003, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885559623, "dur": 1077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751465885560730, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885560801, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_636C45FB8E78E9FC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751465885560919, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751465885561022, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885561721, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751465885561935, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751465885562069, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751465885562295, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751465885562600, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885563192, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885563440, "dur": 696, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751465885565458, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751465885563440, "dur": 2805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885566245, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885567127, "dur": 831, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Editor\\2D\\GameObjectCreation.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751465885566793, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885568227, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Blocks\\Implementations\\Collision\\CollisionShape.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751465885568158, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885569363, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885569613, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885570401, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885571120, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885572233, "dur": 865, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Nodes\\Input\\Geometry\\NormalVectorNode.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751465885571892, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885573422, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885574727, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\ExposeDescriptor.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751465885574097, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885575923, "dur": 747, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\Framework\\Events\\BoltUnityEvent.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751465885575447, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885576817, "dur": 560, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Analysis\\IAnalysis.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751465885576792, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885577753, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885577939, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885578489, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885578753, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885579250, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885579696, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Plugins\\HID\\HIDSupport.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751465885579696, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885580629, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885580830, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885581445, "dur": 6561, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@0a21eb82d95c\\UnityEditor.TestRunner\\TestRun\\Tasks\\Platform\\PlatformSpecificPostBuildTask.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751465885581413, "dur": 6603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885588017, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751465885588215, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751465885588828, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885588915, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751465885589080, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751465885589542, "dur": 1859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885591402, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751465885591569, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751465885592400, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751465885592564, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751465885593347, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885593425, "dur": 1609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885595037, "dur": 286403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885881446, "dur": 2240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751465885883687, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885884143, "dur": 2208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751465885886353, "dur": 1738, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885888098, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751465885890530, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885890587, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751465885892906, "dur": 2878, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751465885895789, "dur": 1982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751465885897832, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1751465885897944, "dur": 56053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885534640, "dur": 25016, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885559656, "dur": 1008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885560822, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FA0E2521CCF3B353.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885560949, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885561598, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751465885561764, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751465885562009, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751465885562156, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751465885562470, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751465885562547, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885562762, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751465885563252, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885564628, "dur": 1263, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Compression.Brotli.dll"}}, {"pid": 12345, "tid": 16, "ts": 1751465885563489, "dur": 2604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885566107, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@676bae148e11\\Editor\\PSDPlugin\\PsdFile\\RleReader.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751465885566094, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885567181, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885567584, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885567951, "dur": 900, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Contexts\\Implementations\\VFXComposedParticleStripOutput.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751465885567951, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885569232, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885569449, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885569649, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\Scopes\\PropertyScope.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751465885569649, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885570861, "dur": 673, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Generation\\Attributes\\GenerateBlocksAttribute.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751465885570774, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885572321, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885573428, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\Volume\\VolumeProfileFactory.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751465885572884, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885574764, "dur": 853, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Runtime\\Utilities\\PropertyBinding\\Implementation\\VFXInputAxisBinder.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751465885574250, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885575754, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885576199, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885577225, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885577681, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885578369, "dur": 936, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\SerializedProperties\\SerializedPropertyProviderAttribute.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751465885578026, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885579419, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885580049, "dur": 725, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Editor\\Settings\\InputEditorUserSettings.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751465885579975, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885581239, "dur": 1576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\math.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751465885581081, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885582941, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885583555, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885584302, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885584580, "dur": 898, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885585482, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885586101, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885586249, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885586468, "dur": 861, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885587335, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885587881, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885588175, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885588821, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885588883, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885589063, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885589638, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885589815, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885591141, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885591285, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885592035, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885592188, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885592352, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885592952, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885593046, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751465885593456, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885593937, "dur": 1085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885595022, "dur": 288446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885883469, "dur": 3287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885886757, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885887365, "dur": 2617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885889983, "dur": 1218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885891206, "dur": 2668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885893913, "dur": 2662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751465885896576, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885896857, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885897245, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885897344, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885897830, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751465885897912, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751465885898222, "dur": 55785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751465885982789, "dur": 1325, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 12116, "tid": 12, "ts": 1751465886005741, "dur": 1962, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 12116, "tid": 12, "ts": 1751465886007848, "dur": 1827, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 12116, "tid": 12, "ts": 1751465885996207, "dur": 14384, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}