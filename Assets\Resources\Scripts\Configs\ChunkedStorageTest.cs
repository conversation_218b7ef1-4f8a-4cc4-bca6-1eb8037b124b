using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Test script to verify chunked character storage performance improvements
/// This script can be attached to a GameObject to run performance tests
/// </summary>
public class ChunkedStorageTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool runTestOnStart = false;
    [SerializeField] private bool enableDetailedLogging = true;
    
    private void Start()
    {
        if (runTestOnStart)
        {
            StartCoroutine(RunPerformanceTest());
        }
    }
    
    /// <summary>
    /// Runs a comprehensive test of the chunked storage system
    /// </summary>
    public IEnumerator RunPerformanceTest()
    {
        Debug.Log("=== CHUNKED STORAGE PERFORMANCE TEST ===");
        
        // Test 1: Migration Test
        yield return StartCoroutine(TestMigration());
        
        // Test 2: Load Performance Test
        yield return StartCoroutine(TestLoadPerformance());
        
        // Test 3: Incremental Save Test
        yield return StartCoroutine(TestIncrementalSave());
        
        // Test 4: Full Save Performance Test
        yield return StartCoroutine(TestFullSavePerformance());
        
        Debug.Log("=== CHUNKED STORAGE TEST COMPLETED ===");
    }
    
    /// <summary>
    /// Tests the migration from monolithic to chunked storage
    /// </summary>
    private IEnumerator TestMigration()
    {
        Debug.Log("[TEST] 🔄 Testing migration to chunked storage...");
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        bool migrationResult = JsonSaveHelper.MigrateToChunkedStorage();
        stopwatch.Stop();
        
        if (migrationResult)
        {
            Debug.Log($"[TEST] ✅ Migration completed in {stopwatch.ElapsedMilliseconds}ms");
            Debug.Log($"[TEST] 📊 Created {JsonSaveHelper.GetChunkFileCount()} chunk files");
        }
        else
        {
            Debug.Log("[TEST] ℹ️ Migration not needed (already using chunked storage or no data)");
        }
        
        yield return null;
    }
    
    /// <summary>
    /// Tests character loading performance
    /// </summary>
    private IEnumerator TestLoadPerformance()
    {
        Debug.Log("[TEST] 📂 Testing character loading performance...");
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var characters = JsonSaveHelper.LoadAllCharactersChunked();
        stopwatch.Stop();
        
        Debug.Log($"[TEST] ✅ Loaded {characters.Count} characters in {stopwatch.ElapsedMilliseconds}ms");
        Debug.Log($"[TEST] 📊 Average: {(float)stopwatch.ElapsedMilliseconds / characters.Count:F2}ms per character");
        
        yield return null;
    }
    
    /// <summary>
    /// Tests incremental saving performance
    /// </summary>
    private IEnumerator TestIncrementalSave()
    {
        Debug.Log("[TEST] 💾 Testing incremental save performance...");
        
        // Load characters
        var characters = JsonSaveHelper.LoadAllCharactersChunked();
        if (characters.Count == 0)
        {
            Debug.LogWarning("[TEST] ⚠️ No characters found for incremental save test");
            yield break;
        }
        
        // Update character mapping
        CharacterChangeTracker.Instance.UpdateCharacterMapping(characters);
        
        // Mark a few characters as dirty
        int testCharacterCount = Mathf.Min(5, characters.Count);
        for (int i = 0; i < testCharacterCount; i++)
        {
            CharacterChangeTracker.Instance.MarkCharacterDirty(characters[i].id);
        }
        
        Debug.Log($"[TEST] 🔄 Marked {testCharacterCount} characters as dirty");
        
        // Test incremental save
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        CharacterChangeTracker.Instance.SaveDirtyCharacters(characters);
        stopwatch.Stop();
        
        Debug.Log($"[TEST] ✅ Incremental save completed in {stopwatch.ElapsedMilliseconds}ms");
        Debug.Log($"[TEST] 📊 Dirty characters: {testCharacterCount}, Total characters: {characters.Count}");
        
        yield return null;
    }
    
    /// <summary>
    /// Tests full save performance for comparison
    /// </summary>
    private IEnumerator TestFullSavePerformance()
    {
        Debug.Log("[TEST] 💾 Testing full save performance...");
        
        // Load characters
        var characters = JsonSaveHelper.LoadAllCharactersChunked();
        if (characters.Count == 0)
        {
            Debug.LogWarning("[TEST] ⚠️ No characters found for full save test");
            yield break;
        }
        
        // Test full save
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        CharacterChangeTracker.Instance.SaveAllCharacters(characters);
        stopwatch.Stop();
        
        Debug.Log($"[TEST] ✅ Full save completed in {stopwatch.ElapsedMilliseconds}ms");
        Debug.Log($"[TEST] 📊 Saved {characters.Count} characters across {JsonSaveHelper.GetChunkFileCount()} chunks");
        Debug.Log($"[TEST] 📊 Average: {(float)stopwatch.ElapsedMilliseconds / characters.Count:F2}ms per character");
        
        yield return null;
    }
    
    /// <summary>
    /// Manual test trigger for editor use
    /// </summary>
    [ContextMenu("Run Performance Test")]
    public void RunTestManually()
    {
        StartCoroutine(RunPerformanceTest());
    }
    
    /// <summary>
    /// Test chunked storage info display
    /// </summary>
    [ContextMenu("Display Storage Info")]
    public void DisplayStorageInfo()
    {
        Debug.Log("=== CHUNKED STORAGE INFO ===");
        Debug.Log($"Using chunked storage: {JsonSaveHelper.IsUsingChunkedStorage()}");
        Debug.Log($"Chunk file count: {JsonSaveHelper.GetChunkFileCount()}");
        Debug.Log($"Characters per chunk: {JsonSaveHelper.CHARACTERS_PER_CHUNK}");
        Debug.Log($"Dirty character count: {CharacterChangeTracker.Instance.GetDirtyCharacterCount()}");
        Debug.Log($"Full save required: {CharacterChangeTracker.Instance.IsFullSaveRequired()}");
        
        if (JsonSaveHelper.IsUsingChunkedStorage())
        {
            var characters = JsonSaveHelper.LoadAllCharactersChunked();
            Debug.Log($"Total characters loaded: {characters.Count}");
            
            int expectedChunks = (int)System.Math.Ceiling((double)characters.Count / JsonSaveHelper.CHARACTERS_PER_CHUNK);
            Debug.Log($"Expected chunks: {expectedChunks}");
        }
    }
    
    /// <summary>
    /// Force migration for testing
    /// </summary>
    [ContextMenu("Force Migration Test")]
    public void ForceMigrationTest()
    {
        Debug.Log("[TEST] 🔄 Forcing migration test...");
        bool result = JsonSaveHelper.MigrateToChunkedStorage();
        Debug.Log($"[TEST] Migration result: {result}");
        DisplayStorageInfo();
    }

    /// <summary>
    /// Test character loading and count verification
    /// </summary>
    [ContextMenu("Verify Character Count")]
    public void VerifyCharacterCount()
    {
        Debug.Log("[TEST] 🔍 Verifying character count across all systems...");

        // Check chunked storage directly
        if (JsonSaveHelper.IsUsingChunkedStorage())
        {
            var chunkedCharacters = JsonSaveHelper.LoadAllCharactersChunked();
            int chunkCount = JsonSaveHelper.GetChunkFileCount();

            Debug.Log($"[TEST] 📂 Chunked Storage: {chunkCount} chunks, {chunkedCharacters.Count} characters loaded");

            // Check each chunk individually
            for (int i = 0; i < chunkCount; i++)
            {
                if (JsonSaveHelper.FileExists($"characters_chunk_{i}.json"))
                {
                    var chunkData = JsonSaveHelper.LoadFromJson<JsonCharactersList>($"characters_chunk_{i}.json");
                    var chunkCharacters = JsonSaveHelper.ConvertCharactersFromJson(chunkData.characters);
                    Debug.Log($"[TEST] 📄 Chunk {i}: {chunkCharacters.Count} characters");
                }
            }
        }

        // Check ConfigsHandler if available
        var configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        if (configsHandler != null)
        {
            var configCharacters = configsHandler.GetCharacters();
            Debug.Log($"[TEST] 🎮 ConfigsHandler: {configCharacters.Count} characters");
        }
        else
        {
            Debug.Log("[TEST] ⚠️ ConfigsHandler not found or not loaded yet");
        }
    }

    /// <summary>
    /// Verify chunk integrity and identify issues
    /// </summary>
    [ContextMenu("Verify Chunk Integrity")]
    public void VerifyChunkIntegrity()
    {
        JsonSaveHelper.VerifyChunkIntegrity();
    }

    /// <summary>
    /// Fix missing chunks from backup
    /// </summary>
    [ContextMenu("Fix Missing Chunks")]
    public void FixMissingChunks()
    {
        bool success = JsonSaveHelper.FixMissingChunks();
        if (success)
        {
            Debug.Log("[TEST] ✅ Chunk fix completed successfully");
        }
        else
        {
            Debug.Log("[TEST] ❌ Chunk fix failed");
        }
    }
}
