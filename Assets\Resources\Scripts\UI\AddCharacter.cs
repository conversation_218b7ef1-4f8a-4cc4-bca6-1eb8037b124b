using UnityEngine;

public class AddCharacter
{
    readonly GameObject valuesPrefab;

    readonly ConfigsHandler configsHandler;

    readonly Transform valuesParentTransform;

    public AddCharacter(int index, ConfigsHandler cH)
    {
        configsHandler = cH;

        // Get the parent transform
        valuesParentTransform = GameObject.Find("CharScrollContent").transform;

        // Load the prefab
        valuesPrefab = Resources.Load<GameObject>("Prefabs/CharacterValues");

        // Instantiate the prefab
        GameObject values = Object.Instantiate(valuesPrefab, valuesParentTransform);

        // set the name
        values.name = "Char" + index;

        // set the character
        values.GetComponent<CharConfUI>().character = configsHandler.GetCharacter(index);
    }
}
