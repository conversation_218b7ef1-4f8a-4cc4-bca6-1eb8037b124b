using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Object pool for managing reusable character UI elements.
/// Provides efficient allocation and deallocation of UI components.
/// </summary>
public class CharacterUIPool : IDisposable
{
    private readonly Queue<CharacterUIElement> availableElements = new Queue<CharacterUIElement>();
    private readonly HashSet<CharacterUIElement> activeElements = new HashSet<CharacterUIElement>();
    private readonly Transform parentTransform;
    private readonly GameObject characterPrefab;
    private readonly int maxPoolSize;
    
    // Pool statistics
    public int ActiveCount => activeElements.Count;
    public int AvailableCount => availableElements.Count;
    public int TotalCount => ActiveCount + AvailableCount;
    
    /// <summary>
    /// Initialize the character UI pool
    /// </summary>
    /// <param name="initialSize">Initial number of UI elements to create</param>
    /// <param name="parent">Parent transform for UI elements</param>
    /// <param name="maxSize">Maximum pool size (0 = unlimited)</param>
    public CharacterUIPool(int initialSize, Transform parent, int maxSize = 0)
    {
        parentTransform = parent ?? throw new ArgumentNullException(nameof(parent));
        maxPoolSize = maxSize;
        
        // Load the character prefab
        characterPrefab = Resources.Load<GameObject>("Prefabs/CharacterValues");
        if (characterPrefab == null)
        {
            throw new InvalidOperationException("Could not load CharacterValues prefab from Resources/Prefabs/");
        }
        
        // Pre-populate the pool
        for (int i = 0; i < initialSize; i++)
        {
            CreateNewElement();
        }
        
        Debug.Log($"[CharacterUIPool] Initialized with {initialSize} elements");
    }
    
    /// <summary>
    /// Get a UI element from the pool
    /// </summary>
    public CharacterUIElement GetFromPool()
    {
        CharacterUIElement element;
        
        if (availableElements.Count > 0)
        {
            // Reuse existing element
            element = availableElements.Dequeue();
        }
        else
        {
            // Create new element if pool is empty and under max size
            if (maxPoolSize == 0 || TotalCount < maxPoolSize)
            {
                element = CreateNewElement();
            }
            else
            {
                Debug.LogWarning("[CharacterUIPool] Pool at maximum capacity, cannot create new element");
                return null;
            }
        }
        
        if (element != null)
        {
            // Activate and track element
            activeElements.Add(element);
            element.gameObject.SetActive(true);
            element.OnReturnedToPool += OnElementReturnedToPool;
        }
        
        return element;
    }
    
    /// <summary>
    /// Return a UI element to the pool
    /// </summary>
    public void ReturnToPool(CharacterUIElement element)
    {
        if (element == null) return;
        
        if (activeElements.Remove(element))
        {
            // Clean up element
            element.OnReturnedToPool -= OnElementReturnedToPool;
            element.Cleanup();
            element.gameObject.SetActive(false);
            
            // Return to available pool
            availableElements.Enqueue(element);
        }
        else
        {
            Debug.LogWarning("[CharacterUIPool] Attempted to return element that wasn't active");
        }
    }
    
    /// <summary>
    /// Return all active elements to the pool
    /// </summary>
    public void ReturnAllToPool()
    {
        // Create a copy of active elements to avoid modification during iteration
        var elementsToReturn = new List<CharacterUIElement>(activeElements);
        
        foreach (var element in elementsToReturn)
        {
            ReturnToPool(element);
        }
    }
    
    /// <summary>
    /// Create a new UI element
    /// </summary>
    private CharacterUIElement CreateNewElement()
    {
        GameObject instance = UnityEngine.Object.Instantiate(characterPrefab, parentTransform);
        instance.SetActive(false);
        
        // Replace CharConfUI with CharacterUIElement or enhance existing component
        CharConfUI existingComponent = instance.GetComponent<CharConfUI>();
        CharacterUIElement uiElement;
        
        if (existingComponent != null)
        {
            // Enhance existing CharConfUI with pooling capabilities
            uiElement = instance.AddComponent<CharacterUIElement>();
            uiElement.Initialize(existingComponent);
        }
        else
        {
            // Add CharacterUIElement component
            uiElement = instance.AddComponent<CharacterUIElement>();
        }
        
        // Set unique name for debugging
        instance.name = $"PooledCharacterUI_{TotalCount}";
        
        return uiElement;
    }
    
    /// <summary>
    /// Handle element returned to pool event
    /// </summary>
    private void OnElementReturnedToPool(CharacterUIElement element)
    {
        ReturnToPool(element);
    }
    
    /// <summary>
    /// Clear the entire pool
    /// </summary>
    public void Clear()
    {
        // Return all active elements
        ReturnAllToPool();
        
        // Destroy all available elements
        while (availableElements.Count > 0)
        {
            var element = availableElements.Dequeue();
            if (element != null && element.gameObject != null)
            {
                UnityEngine.Object.Destroy(element.gameObject);
            }
        }
        
        Debug.Log("[CharacterUIPool] Pool cleared");
    }
    
    /// <summary>
    /// Resize the pool to target size
    /// </summary>
    public void Resize(int targetSize)
    {
        int currentSize = TotalCount;
        
        if (targetSize > currentSize)
        {
            // Add more elements
            int elementsToAdd = targetSize - currentSize;
            for (int i = 0; i < elementsToAdd; i++)
            {
                if (maxPoolSize == 0 || TotalCount < maxPoolSize)
                {
                    CreateNewElement();
                }
                else
                {
                    break;
                }
            }
        }
        else if (targetSize < currentSize)
        {
            // Remove excess elements (only from available pool)
            int elementsToRemove = currentSize - targetSize;
            int removed = 0;
            
            while (availableElements.Count > 0 && removed < elementsToRemove)
            {
                var element = availableElements.Dequeue();
                if (element != null && element.gameObject != null)
                {
                    UnityEngine.Object.Destroy(element.gameObject);
                    removed++;
                }
            }
        }
        
        Debug.Log($"[CharacterUIPool] Resized to {TotalCount} elements (target: {targetSize})");
    }
    
    /// <summary>
    /// Get pool statistics for debugging
    /// </summary>
    public string GetPoolStats()
    {
        return $"Pool Stats - Active: {ActiveCount}, Available: {AvailableCount}, Total: {TotalCount}, Max: {(maxPoolSize == 0 ? "Unlimited" : maxPoolSize.ToString())}";
    }
    
    /// <summary>
    /// Dispose of the pool and cleanup resources
    /// </summary>
    public void Dispose()
    {
        Clear();
        Debug.Log("[CharacterUIPool] Disposed");
    }
}

/// <summary>
/// Virtual item representing a character in the virtualized list
/// </summary>
public class VirtualCharacterItem
{
    public int Index { get; }
    public BattleCharacter Character { get; }
    public bool IsVisible { get; set; }
    public bool IsDirty { get; set; }
    
    public VirtualCharacterItem(int index, BattleCharacter character)
    {
        Index = index;
        Character = character ?? throw new ArgumentNullException(nameof(character));
        IsVisible = false;
        IsDirty = false;
    }
    
    public void MarkDirty()
    {
        IsDirty = true;
    }
    
    public void ClearDirty()
    {
        IsDirty = false;
    }
}
