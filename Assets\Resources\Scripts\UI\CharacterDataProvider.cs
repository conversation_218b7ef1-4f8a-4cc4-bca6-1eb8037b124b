using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// Concrete implementation of ICharacterDataProvider that integrates with ConfigsHandler.
/// Provides character data access and change notifications for the virtual scroll system.
/// </summary>
public class CharacterDataProvider : ICharacterDataProvider
{
    // Events
    public event Action OnDataChanged;
    public event Action<int> OnItemChanged;
    
    // Data source
    private ConfigsHandler configsHandler;
    private List<BattleCharacter> cachedCharacters;
    private bool isInitialized = false;
    
    // Properties
    public bool IsReady => isInitialized && configsHandler != null && cachedCharacters != null;
    
    /// <summary>
    /// Initialize the data provider with Configs<PERSON>and<PERSON>
    /// </summary>
    public CharacterDataProvider(ConfigsHandler configsHandler)
    {
        this.configsHandler = configsHandler ?? throw new ArgumentNullException(nameof(configsHandler));
        Initialize();
    }
    
    /// <summary>
    /// Initialize the data provider
    /// </summary>
    private void Initialize()
    {
        try
        {
            RefreshData();
            isInitialized = true;
            Debug.Log($"[CharacterDataProvider] Initialized with {GetCharacterCount()} characters");
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CharacterDataProvider] Failed to initialize: {ex.Message}");
            isInitialized = false;
        }
    }
    
    /// <summary>
    /// Get the total number of characters
    /// </summary>
    public int GetCharacterCount()
    {
        return cachedCharacters?.Count ?? 0;
    }
    
    /// <summary>
    /// Get character at specific index
    /// </summary>
    public BattleCharacter GetCharacter(int index)
    {
        if (!IsValidIndex(index))
            return null;
            
        return cachedCharacters[index];
    }
    
    /// <summary>
    /// Get character by ID
    /// </summary>
    public BattleCharacter GetCharacterById(string id)
    {
        if (string.IsNullOrEmpty(id) || cachedCharacters == null)
            return null;
            
        return cachedCharacters.FirstOrDefault(c => c.id == id);
    }
    
    /// <summary>
    /// Find the index of a character by ID
    /// </summary>
    public int FindCharacterIndex(string id)
    {
        if (string.IsNullOrEmpty(id) || cachedCharacters == null)
            return -1;
            
        for (int i = 0; i < cachedCharacters.Count; i++)
        {
            if (cachedCharacters[i].id == id)
                return i;
        }
        
        return -1;
    }
    
    /// <summary>
    /// Update character data at specific index
    /// </summary>
    public bool UpdateCharacter(int index, BattleCharacter character)
    {
        if (!IsValidIndex(index) || character == null)
            return false;
            
        try
        {
            // Update cached data
            cachedCharacters[index] = character;
            
            // Update in ConfigsHandler
            configsHandler.SetCharacter(character, index);
            
            // Notify listeners
            OnItemChanged?.Invoke(index);
            
            Debug.Log($"[CharacterDataProvider] Updated character at index {index}: {character.name}");
            return true;
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CharacterDataProvider] Failed to update character at index {index}: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// Add a new character
    /// </summary>
    public int AddCharacter(BattleCharacter character)
    {
        if (character == null || cachedCharacters == null)
            return -1;

        try
        {
            // ConfigsHandler.AddCharacter() creates a new character and returns its index
            // We'll use that method and then update the character data if needed
            int newIndex = configsHandler.AddCharacter();

            // If we want to use the provided character data, update it
            if (newIndex >= 0 && character != null)
            {
                configsHandler.SetCharacter(character, newIndex);
            }

            // Refresh cached data
            RefreshData();

            Debug.Log($"[CharacterDataProvider] Added character at index {newIndex}");
            return newIndex;
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CharacterDataProvider] Failed to add character: {ex.Message}");
            return -1;
        }
    }
    
    /// <summary>
    /// Remove character at specific index
    /// </summary>
    public bool RemoveCharacter(int index)
    {
        if (!IsValidIndex(index))
            return false;
            
        try
        {
            var character = cachedCharacters[index];
            
            // Remove from cached data
            cachedCharacters.RemoveAt(index);
            
            // Remove from ConfigsHandler
            configsHandler.RemoveCharacter(character);
            
            // Notify listeners
            OnDataChanged?.Invoke();
            
            Debug.Log($"[CharacterDataProvider] Removed character at index {index}: {character.name}");
            return true;
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CharacterDataProvider] Failed to remove character at index {index}: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// Remove character by ID
    /// </summary>
    public bool RemoveCharacterById(string id)
    {
        int index = FindCharacterIndex(id);
        return index >= 0 && RemoveCharacter(index);
    }
    
    /// <summary>
    /// Refresh data from ConfigsHandler
    /// </summary>
    public void RefreshData()
    {
        try
        {
            if (configsHandler == null)
            {
                Debug.LogError("[CharacterDataProvider] ConfigsHandler is null, cannot refresh data");
                return;
            }

            // Get fresh data from ConfigsHandler using public method
            var characters = configsHandler.GetCharacters();
            cachedCharacters = new List<BattleCharacter>(characters);

            // Notify listeners of data change
            OnDataChanged?.Invoke();

            Debug.Log($"[CharacterDataProvider] Refreshed data - {cachedCharacters.Count} characters loaded");
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CharacterDataProvider] Failed to refresh data: {ex.Message}");
            cachedCharacters = new List<BattleCharacter>();
        }
    }
    
    /// <summary>
    /// Check if index is valid
    /// </summary>
    private bool IsValidIndex(int index)
    {
        return cachedCharacters != null && index >= 0 && index < cachedCharacters.Count;
    }
    
    /// <summary>
    /// Notify that a specific character changed
    /// </summary>
    public void NotifyCharacterChanged(int index)
    {
        if (IsValidIndex(index))
        {
            OnItemChanged?.Invoke(index);
        }
    }
    
    /// <summary>
    /// Notify that a character changed by ID
    /// </summary>
    public void NotifyCharacterChanged(string id)
    {
        int index = FindCharacterIndex(id);
        if (index >= 0)
        {
            OnItemChanged?.Invoke(index);
        }
    }
    
    /// <summary>
    /// Get debug information about the data provider
    /// </summary>
    public string GetDebugInfo()
    {
        return $"CharacterDataProvider - Ready: {IsReady}, " +
               $"Characters: {GetCharacterCount()}, " +
               $"ConfigsHandler: {(configsHandler != null ? "Valid" : "Null")}";
    }
}
