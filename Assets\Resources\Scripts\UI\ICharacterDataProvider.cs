using System;

/// <summary>
/// Interface for providing character data to the virtual scroll system.
/// Abstracts character data access and provides change notifications.
/// </summary>
public interface ICharacterDataProvider
{
    /// <summary>
    /// Event fired when the entire character dataset changes (add/remove characters)
    /// </summary>
    event Action OnDataChanged;
    
    /// <summary>
    /// Event fired when a specific character's data changes
    /// </summary>
    event Action<int> OnItemChanged;
    
    /// <summary>
    /// Get the total number of characters
    /// </summary>
    int GetCharacterCount();
    
    /// <summary>
    /// Get character at specific index
    /// </summary>
    /// <param name="index">Zero-based index</param>
    /// <returns>Character at index, or null if invalid index</returns>
    BattleCharacter GetCharacter(int index);
    
    /// <summary>
    /// Get character by ID
    /// </summary>
    /// <param name="id">Character ID</param>
    /// <returns>Character with matching ID, or null if not found</returns>
    BattleCharacter GetCharacterById(string id);
    
    /// <summary>
    /// Find the index of a character by ID
    /// </summary>
    /// <param name="id">Character ID</param>
    /// <returns>Index of character, or -1 if not found</returns>
    int FindCharacterIndex(string id);
    
    /// <summary>
    /// Update character data at specific index
    /// </summary>
    /// <param name="index">Character index</param>
    /// <param name="character">Updated character data</param>
    /// <returns>True if update successful</returns>
    bool UpdateCharacter(int index, BattleCharacter character);
    
    /// <summary>
    /// Add a new character
    /// </summary>
    /// <param name="character">Character to add</param>
    /// <returns>Index of added character, or -1 if failed</returns>
    int AddCharacter(BattleCharacter character);
    
    /// <summary>
    /// Remove character at specific index
    /// </summary>
    /// <param name="index">Index of character to remove</param>
    /// <returns>True if removal successful</returns>
    bool RemoveCharacter(int index);
    
    /// <summary>
    /// Remove character by ID
    /// </summary>
    /// <param name="id">ID of character to remove</param>
    /// <returns>True if removal successful</returns>
    bool RemoveCharacterById(string id);
    
    /// <summary>
    /// Refresh data from source (useful for external changes)
    /// </summary>
    void RefreshData();
    
    /// <summary>
    /// Check if data provider is ready for use
    /// </summary>
    bool IsReady { get; }
}
