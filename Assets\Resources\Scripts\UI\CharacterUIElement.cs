using System;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Enhanced character UI element that supports object pooling and virtualization.
/// Wraps the existing CharConfUI functionality while adding pooling capabilities.
/// </summary>
public class CharacterUIElement : MonoBehaviour
{
    // Events
    public event Action<CharacterUIElement> OnReturnedToPool;
    
    // References
    private CharConfUI charConfUI;
    private RectTransform rectTransform;
    private BattleCharacter currentCharacter;
    private int currentIndex = -1;
    
    // Pooling state
    private bool isInitialized = false;
    private bool isActive = false;
    
    // Properties
    public BattleCharacter Character => currentCharacter;
    public int Index => currentIndex;
    public bool IsActive => isActive;
    public RectTransform RectTransform => rectTransform;
    
    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        charConfUI = GetComponent<CharConfUI>();
    }
    
    /// <summary>
    /// Initialize the UI element with existing CharConfUI component
    /// </summary>
    public void Initialize(CharConfUI existingCharConfUI)
    {
        charConfUI = existingCharConfUI ?? throw new ArgumentNullException(nameof(existingCharConfUI));
        
        if (rectTransform == null)
            rectTransform = GetComponent<RectTransform>();
            
        isInitialized = true;
    }
    
    /// <summary>
    /// Bind character data to this UI element
    /// </summary>
    public void BindCharacter(BattleCharacter character, int index)
    {
        if (!isInitialized)
        {
            Debug.LogError("[CharacterUIElement] Element not initialized before binding character");
            return;
        }
        
        currentCharacter = character;
        currentIndex = index;
        isActive = true;
        
        // Update the underlying CharConfUI component
        if (charConfUI != null)
        {
            charConfUI.character = character;
            
            // Trigger the CharConfUI initialization if needed
            if (charConfUI.enabled)
            {
                // The CharConfUI Start() method will handle the UI setup
                // We need to ensure it gets the updated character reference
                RefreshUI();
            }
        }
        
        // Set the name for debugging
        gameObject.name = $"CharacterUI_{index}_{character?.name ?? "Unknown"}";
    }
    
    /// <summary>
    /// Refresh the UI display with current character data
    /// </summary>
    public void RefreshUI()
    {
        if (charConfUI == null || currentCharacter == null) return;

        // Update UI elements directly using the same structure as CharConfUI
        try
        {
            // Based on CharConfUI.Start() method:
            // Child 0: enemy button
            // Child 1: name input field
            // Child 2: stats button
            // Child 3: skills button
            // Child 4: mods button
            // Child 5: level input field

            var nameInput = transform.GetChild(1)?.GetComponent<TMPro.TMP_InputField>();
            var levelInput = transform.GetChild(5)?.GetComponent<TMPro.TMP_InputField>();
            var enemyButton = transform.GetChild(0)?.GetComponent<Button>();

            if (nameInput != null)
                nameInput.text = currentCharacter.name;

            if (levelInput != null)
                levelInput.text = currentCharacter.level.ToString();

            // Update enemy button appearance based on character.isEnemy
            if (enemyButton != null)
            {
                // The enemy button likely has a visual indicator for enemy status
                // We'll let the CharConfUI handle the visual state through its Update method
                // Just ensure the character reference is correct
                charConfUI.character = currentCharacter;
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CharacterUIElement] Error refreshing UI for character {currentCharacter.name}: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Set the position of this UI element
    /// </summary>
    public void SetPosition(Vector2 position)
    {
        if (rectTransform != null)
        {
            rectTransform.anchoredPosition = position;
        }
    }
    
    /// <summary>
    /// Set the size of this UI element
    /// </summary>
    public void SetSize(Vector2 size)
    {
        if (rectTransform != null)
        {
            rectTransform.sizeDelta = size;
        }
    }
    
    /// <summary>
    /// Clean up the UI element when returning to pool
    /// </summary>
    public void Cleanup()
    {
        // Clear character reference
        if (charConfUI != null)
        {
            charConfUI.character = null;
        }
        
        currentCharacter = null;
        currentIndex = -1;
        isActive = false;
        
        // Reset UI state
        ResetUIState();
        
        // Reset name
        gameObject.name = "PooledCharacterUI_Inactive";
    }
    
    /// <summary>
    /// Reset UI elements to default state
    /// </summary>
    private void ResetUIState()
    {
        try
        {
            // Reset input fields using correct child indices
            var nameInput = transform.GetChild(1)?.GetComponent<TMPro.TMP_InputField>();
            var levelInput = transform.GetChild(5)?.GetComponent<TMPro.TMP_InputField>();

            if (nameInput != null)
                nameInput.text = "";

            if (levelInput != null)
                levelInput.text = "1";

            // Clear character reference
            if (charConfUI != null)
                charConfUI.character = null;
        }
        catch (Exception ex)
        {
            Debug.LogWarning($"[CharacterUIElement] Error resetting UI state: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Return this element to the pool
    /// </summary>
    public void ReturnToPool()
    {
        OnReturnedToPool?.Invoke(this);
    }
    
    /// <summary>
    /// Get the character at the specified index from ConfigsHandler
    /// </summary>
    private BattleCharacter GetCharacterFromConfigsHandler(int index)
    {
        try
        {
            var configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
            return configsHandler?.GetCharacter(index);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CharacterUIElement] Error getting character from ConfigsHandler: {ex.Message}");
            return null;
        }
    }
    
    /// <summary>
    /// Handle character data updates
    /// </summary>
    public void OnCharacterDataChanged()
    {
        if (isActive && currentCharacter != null)
        {
            // Refresh the character data from the source
            var updatedCharacter = GetCharacterFromConfigsHandler(currentIndex);
            if (updatedCharacter != null)
            {
                currentCharacter = updatedCharacter;
                RefreshUI();
            }
        }
    }
    
    /// <summary>
    /// Validate the UI element state
    /// </summary>
    public bool ValidateState()
    {
        if (!isInitialized)
        {
            Debug.LogWarning("[CharacterUIElement] Element not initialized");
            return false;
        }
        
        if (isActive && currentCharacter == null)
        {
            Debug.LogWarning("[CharacterUIElement] Active element has no character data");
            return false;
        }
        
        if (charConfUI == null)
        {
            Debug.LogWarning("[CharacterUIElement] Missing CharConfUI component");
            return false;
        }
        
        return true;
    }
    
    private void OnDestroy()
    {
        // Cleanup when destroyed
        Cleanup();
    }
    
    // Debug information
    public string GetDebugInfo()
    {
        return $"CharacterUIElement - Index: {currentIndex}, " +
               $"Character: {currentCharacter?.name ?? "None"}, " +
               $"Active: {isActive}, Initialized: {isInitialized}";
    }
}
