using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;

/// <summary>
/// Helper class for JSON serialization and deserialization of save data
/// Provides methods to convert between current data structures and JSON format
/// Supports chunked character storage for improved performance with large datasets
/// </summary>
public static class JsonSaveHelper
{
    /// <summary>
    /// Gets the save folder path (same logic as LoadValues.cs)
    /// </summary>
    public static string GetSaveFolder()
    {
        return (Application.platform == RuntimePlatform.WindowsEditor || Application.platform == RuntimePlatform.WindowsPlayer) 
            ? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), @"DKGRPGPrototype_unity") 
            : Application.persistentDataPath;
    }

    /// <summary>
    /// Saves data to JSON file
    /// </summary>
    public static void SaveToJson<T>(T data, string fileName)
    {
        string saveFolder = GetSaveFolder();

        if (!Directory.Exists(saveFolder))
        {
            Directory.CreateDirectory(saveFolder);
        }

        string filePath = Path.Combine(saveFolder, fileName);

        try
        {
            string json = JsonUtility.ToJson(data, true);
            File.WriteAllText(filePath, json);
        }
        catch (Exception ex)
        {
            Debug.LogError($"[JsonSaveHelper] ❌ Failed to save {fileName}: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Loads data from JSON file
    /// </summary>
    public static T LoadFromJson<T>(string fileName) where T : new()
    {
        string saveFolder = GetSaveFolder();
        string filePath = Path.Combine(saveFolder, fileName);

        if (!File.Exists(filePath))
        {
            return new T();
        }

        try
        {
            string json = File.ReadAllText(filePath);

            if (string.IsNullOrEmpty(json))
            {
                Debug.LogWarning($"[MIGRATION] ⚠️ {fileName} - JSON file is empty");
                return new T();
            }

            T result = JsonUtility.FromJson<T>(json);
            return result;
        }
        catch (Exception ex)
        {
            Debug.LogError($"[MIGRATION] ❌ {fileName} - JSON parsing failed: {ex.Message}");
            return new T();
        }
    }

    /// <summary>
    /// Checks if a file exists in the save folder
    /// </summary>
    public static bool FileExists(string fileName)
    {
        string saveFolder = GetSaveFolder();
        string filePath = Path.Combine(saveFolder, fileName);
        return File.Exists(filePath);
    }

    /// <summary>
    /// Migrates old format file to new JSON format
    /// </summary>
    public static bool MigrateToJson(string oldFileName, string newJsonFileName, Func<string, object> migrationFunc)
    {
        string saveFolder = GetSaveFolder();
        string oldFilePath = Path.Combine(saveFolder, oldFileName);
        string newFilePath = Path.Combine(saveFolder, newJsonFileName);

        if (!File.Exists(oldFilePath) || File.Exists(newFilePath))
            return false;

        try
        {
            string oldContent = File.ReadAllText(oldFilePath);
            object migratedData = migrationFunc(oldContent);
            string json = JsonUtility.ToJson(migratedData, true);
            File.WriteAllText(newFilePath, json);

            // Keep old file as backup with .bak extension
            File.Move(oldFilePath, oldFilePath + ".bak");
            return true;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Failed to migrate {oldFileName} to {newJsonFileName}: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Migrates monolithic characters.json to chunked storage system
    /// </summary>
    public static bool MigrateToChunkedStorage()
    {
        string saveFolder = GetSaveFolder();
        string oldFilePath = Path.Combine(saveFolder, "characters.json");
        string firstChunkPath = Path.Combine(saveFolder, "characters_chunk_0.json");

        // Only migrate if old file exists and chunked files don't exist yet
        if (!File.Exists(oldFilePath) || File.Exists(firstChunkPath))
        {
            return false;
        }

        try
        {
            Debug.Log("[MIGRATION] 🔄 Starting migration from monolithic to chunked character storage...");

            // Load all characters from the old file
            var charactersData = LoadFromJson<JsonCharactersList>("characters.json");
            var allCharacters = ConvertCharactersFromJson(charactersData.characters);

            Debug.Log($"[MIGRATION] 📊 Found {allCharacters.Count} characters to migrate");

            // Split into chunks and save
            int totalChunks = (int)Math.Ceiling((double)allCharacters.Count / CHARACTERS_PER_CHUNK);

            for (int chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++)
            {
                SaveCharacterChunk(allCharacters, chunkIndex);
            }

            // Create backup of original file
            string backupPath = Path.Combine(saveFolder, "characters_backup_before_chunking.json");
            File.Copy(oldFilePath, backupPath, true);

            // Remove original file
            File.Delete(oldFilePath);

            Debug.Log($"[MIGRATION] ✅ Successfully migrated {allCharacters.Count} characters to {totalChunks} chunks");
            Debug.Log($"[MIGRATION] 💾 Original file backed up as: characters_backup_before_chunking.json");

            return true;
        }
        catch (Exception ex)
        {
            Debug.LogError($"[MIGRATION] ❌ Failed to migrate to chunked storage: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Checks if the system is using chunked storage
    /// </summary>
    public static bool IsUsingChunkedStorage()
    {
        return FileExists("characters_chunk_0.json");
    }

    /// <summary>
    /// Gets the total number of chunk files that exist
    /// </summary>
    public static int GetChunkFileCount()
    {
        int count = 0;
        while (FileExists($"characters_chunk_{count}.json"))
        {
            count++;
        }
        return count;
    }

    /// <summary>
    /// Verifies chunk integrity and reports missing chunks
    /// </summary>
    public static void VerifyChunkIntegrity()
    {
        if (!IsUsingChunkedStorage())
        {
            Debug.Log("[CHUNK_VERIFY] Not using chunked storage");
            return;
        }

        var allCharacters = LoadAllCharactersChunked();
        int totalCharacters = allCharacters.Count;
        int chunkCount = GetChunkFileCount();
        int expectedChunks = (int)Math.Ceiling((double)totalCharacters / CHARACTERS_PER_CHUNK);

        Debug.Log($"[CHUNK_VERIFY] Total characters loaded: {totalCharacters}");
        Debug.Log($"[CHUNK_VERIFY] Chunk files found: {chunkCount}");
        Debug.Log($"[CHUNK_VERIFY] Expected chunks for {totalCharacters} characters: {expectedChunks}");

        // Check for missing chunks
        for (int i = 0; i < expectedChunks; i++)
        {
            if (!FileExists($"characters_chunk_{i}.json"))
            {
                Debug.LogWarning($"[CHUNK_VERIFY] Missing chunk file: characters_chunk_{i}.json");
            }
        }

        // Check if backup file has more characters
        if (FileExists("characters_backup_before_chunking.json"))
        {
            try
            {
                var backupData = LoadFromJson<JsonCharactersList>("characters_backup_before_chunking.json");
                var backupCharacters = ConvertCharactersFromJson(backupData.characters);
                Debug.Log($"[CHUNK_VERIFY] Backup file contains: {backupCharacters.Count} characters");

                if (backupCharacters.Count > totalCharacters)
                {
                    Debug.LogWarning($"[CHUNK_VERIFY] Backup has {backupCharacters.Count - totalCharacters} more characters than chunks!");
                    Debug.LogWarning("[CHUNK_VERIFY] Consider re-running migration to fix missing characters");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CHUNK_VERIFY] Failed to read backup file: {ex.Message}");
            }
        }
    }

    // ===== CHUNKED CHARACTER STORAGE SYSTEM =====

    /// <summary>
    /// Number of characters per chunk file for optimal performance
    /// </summary>
    public const int CHARACTERS_PER_CHUNK = 50;

    /// <summary>
    /// Fixes missing chunks by re-migrating from backup file
    /// </summary>
    public static bool FixMissingChunks()
    {
        if (!FileExists("characters_backup_before_chunking.json"))
        {
            Debug.LogError("[CHUNK_FIX] No backup file found to restore from");
            return false;
        }

        try
        {
            Debug.Log("[CHUNK_FIX] 🔧 Fixing missing chunks from backup file...");

            // Load all characters from backup
            var backupData = LoadFromJson<JsonCharactersList>("characters_backup_before_chunking.json");
            var allCharacters = ConvertCharactersFromJson(backupData.characters);

            Debug.Log($"[CHUNK_FIX] 📂 Loaded {allCharacters.Count} characters from backup");

            // Re-create all chunks
            int totalChunks = (int)Math.Ceiling((double)allCharacters.Count / CHARACTERS_PER_CHUNK);

            for (int chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++)
            {
                SaveCharacterChunk(allCharacters, chunkIndex);
            }

            Debug.Log($"[CHUNK_FIX] ✅ Successfully recreated {totalChunks} chunks from backup");

            // Verify the fix
            VerifyChunkIntegrity();

            return true;
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CHUNK_FIX] ❌ Failed to fix chunks: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Saves a specific chunk of characters to a chunk file
    /// </summary>
    public static void SaveCharacterChunk(List<BattleCharacter> allCharacters, int chunkIndex)
    {
        try
        {
            int startIndex = chunkIndex * CHARACTERS_PER_CHUNK;
            var chunk = allCharacters.Skip(startIndex).Take(CHARACTERS_PER_CHUNK).ToList();

            if (chunk.Count == 0)
            {
                // If chunk is empty, delete the chunk file if it exists
                string chunkFileName = $"characters_chunk_{chunkIndex}.json";
                if (FileExists(chunkFileName))
                {
                    string filePath = Path.Combine(GetSaveFolder(), chunkFileName);
                    File.Delete(filePath);
                    Debug.Log($"[CHUNKED] 🗑️ Deleted empty chunk file: {chunkFileName}");
                }
                return;
            }

            var jsonChunk = ConvertCharactersToJson(chunk);
            var chunkWrapper = new JsonCharactersList(jsonChunk);

            string fileName = $"characters_chunk_{chunkIndex}.json";
            SaveToJson(chunkWrapper, fileName);

            Debug.Log($"[CHUNKED] 💾 Saved chunk {chunkIndex} with {chunk.Count} characters");
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CHUNKED] ❌ Failed to save chunk {chunkIndex}: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Loads all characters from chunked files
    /// </summary>
    public static List<BattleCharacter> LoadAllCharactersChunked()
    {
        var allCharacters = new List<BattleCharacter>();
        int chunkIndex = 0;
        int totalChunksLoaded = 0;

        try
        {
            while (FileExists($"characters_chunk_{chunkIndex}.json"))
            {
                var chunkData = LoadFromJson<JsonCharactersList>($"characters_chunk_{chunkIndex}.json");
                var chunkCharacters = ConvertCharactersFromJson(chunkData.characters);
                allCharacters.AddRange(chunkCharacters);

                totalChunksLoaded++;
                chunkIndex++;

                // Yield control periodically for large datasets
                if (chunkIndex % 5 == 0)
                {
                    Debug.Log($"[CHUNKED] 📂 Loaded {totalChunksLoaded} chunks, {allCharacters.Count} characters so far...");
                }
            }

            Debug.Log($"[CHUNKED] ✅ Successfully loaded {allCharacters.Count} characters from {totalChunksLoaded} chunks");
        }
        catch (Exception ex)
        {
            Debug.LogError($"[CHUNKED] ❌ Failed to load chunked characters: {ex.Message}");
            throw;
        }

        return allCharacters;
    }

    /// <summary>
    /// Gets the chunk index for a specific character position
    /// </summary>
    public static int GetChunkIndexForCharacter(int characterIndex)
    {
        return characterIndex / CHARACTERS_PER_CHUNK;
    }

    /// <summary>
    /// Gets all chunk indices that need to be saved for a list of character indices
    /// </summary>
    public static HashSet<int> GetAffectedChunks(IEnumerable<int> characterIndices)
    {
        var affectedChunks = new HashSet<int>();
        foreach (int index in characterIndices)
        {
            affectedChunks.Add(GetChunkIndexForCharacter(index));
        }
        return affectedChunks;
    }

    /// <summary>
    /// Converts list of BattleCharacters to JSON format
    /// </summary>
    public static List<JsonBattleCharacter> ConvertCharactersToJson(List<BattleCharacter> characters)
    {
        if (characters == null)
        {
            return new List<JsonBattleCharacter>();
        }

        List<JsonBattleCharacter> jsonCharacters = new List<JsonBattleCharacter>();
        for (int i = 0; i < characters.Count; i++)
        {
            var character = characters[i];
            if (character == null) continue;

            try
            {
                JsonBattleCharacter jsonChar = new JsonBattleCharacter(character);
                jsonCharacters.Add(jsonChar);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[JsonSaveHelper] ❌ Failed to convert character {character.id}: {ex.Message}");
            }
        }

        return jsonCharacters;
    }

    /// <summary>
    /// Converts JSON characters back to BattleCharacter list
    /// </summary>
    public static List<BattleCharacter> ConvertCharactersFromJson(List<JsonBattleCharacter> jsonCharacters)
    {
        if (jsonCharacters == null)
        {
            return new List<BattleCharacter>();
        }

        List<BattleCharacter> characters = new List<BattleCharacter>();
        for (int i = 0; i < jsonCharacters.Count; i++)
        {
            var jsonChar = jsonCharacters[i];
            if (jsonChar == null) continue;

            try
            {
                BattleCharacter character = jsonChar.ToBattleCharacter();
                characters.Add(character);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[JsonSaveHelper] ❌ Failed to convert JSON character {jsonChar.id}: {ex.Message}");
            }
        }

        return characters;
    }

    /// <summary>
    /// Converts list of BuffNDebuffs to JSON format
    /// </summary>
    public static List<JsonBuffNDebuffs> ConvertBuffsToJson(List<BuffNDebuffs> buffs)
    {
        List<JsonBuffNDebuffs> jsonBuffs = new List<JsonBuffNDebuffs>();
        foreach (var buff in buffs)
        {
            jsonBuffs.Add(new JsonBuffNDebuffs(buff));
        }
        return jsonBuffs;
    }

    /// <summary>
    /// Converts JSON buffs back to BuffNDebuffs list
    /// </summary>
    public static List<BuffNDebuffs> ConvertBuffsFromJson(List<JsonBuffNDebuffs> jsonBuffs)
    {
        List<BuffNDebuffs> buffs = new List<BuffNDebuffs>();
        for (int i = 0; i < jsonBuffs.Count; i++)
        {
            buffs.Add(jsonBuffs[i].ToBuffNDebuffs(i));
        }
        return buffs;
    }

    /// <summary>
    /// Converts list of PartyCharacters to JSON format
    /// </summary>
    public static List<JsonPartyCharacters> ConvertPartiesToJson(List<PartyCharacters> parties)
    {
        List<JsonPartyCharacters> jsonParties = new List<JsonPartyCharacters>();
        foreach (var party in parties)
        {
            jsonParties.Add(new JsonPartyCharacters(party));
        }
        return jsonParties;
    }

    /// <summary>
    /// Converts JSON parties back to PartyCharacters list
    /// </summary>
    public static List<PartyCharacters> ConvertPartiesFromJson(List<JsonPartyCharacters> jsonParties, List<BattleCharacter> allCharacters)
    {
        List<PartyCharacters> parties = new List<PartyCharacters>();
        foreach (var jsonParty in jsonParties)
        {
            parties.Add(jsonParty.ToPartyCharacters(allCharacters));
        }
        return parties;
    }

    /// <summary>
    /// Converts list of CharacterSkills to JSON format
    /// </summary>
    public static List<JsonCharacterSkills> ConvertSkillsToJson(List<CharacterSkills> skills)
    {
        List<JsonCharacterSkills> jsonSkills = new List<JsonCharacterSkills>();
        foreach (var skill in skills)
        {
            jsonSkills.Add(new JsonCharacterSkills(skill));
        }
        return jsonSkills;
    }

    /// <summary>
    /// Converts JSON skills back to CharacterSkills list
    /// </summary>
    public static List<CharacterSkills> ConvertSkillsFromJson(List<JsonCharacterSkills> jsonSkills)
    {
        List<CharacterSkills> skills = new List<CharacterSkills>();
        foreach (var jsonSkill in jsonSkills)
        {
            skills.Add(jsonSkill.ToCharacterSkills());
        }
        return skills;
    }

    /// <summary>
    /// Converts list of CharacterStatus to JSON format
    /// </summary>
    public static List<JsonCharacterStatus> ConvertStatsToJson(List<CharacterStatus> stats)
    {
        List<JsonCharacterStatus> jsonStats = new List<JsonCharacterStatus>();
        foreach (var stat in stats)
        {
            jsonStats.Add(new JsonCharacterStatus(stat));
        }
        return jsonStats;
    }

    /// <summary>
    /// Converts JSON stats back to CharacterStatus list
    /// </summary>
    public static List<CharacterStatus> ConvertStatsFromJson(List<JsonCharacterStatus> jsonStats)
    {
        List<CharacterStatus> stats = new List<CharacterStatus>();
        foreach (var jsonStat in jsonStats)
        {
            stats.Add(jsonStat.ToCharacterStatus());
        }
        return stats;
    }

    /// <summary>
    /// Converts list of CharacterMods to JSON format
    /// </summary>
    public static List<JsonCharacterMods> ConvertModsToJson(List<CharacterMods> mods)
    {
        List<JsonCharacterMods> jsonMods = new List<JsonCharacterMods>();
        foreach (var mod in mods)
        {
            jsonMods.Add(new JsonCharacterMods(mod));
        }
        return jsonMods;
    }

    /// <summary>
    /// Converts JSON mods back to CharacterMods list
    /// </summary>
    public static List<CharacterMods> ConvertModsFromJson(List<JsonCharacterMods> jsonMods)
    {
        List<CharacterMods> mods = new List<CharacterMods>();
        foreach (var jsonMod in jsonMods)
        {
            mods.Add(jsonMod.ToCharacterMods());
        }
        return mods;
    }
}
