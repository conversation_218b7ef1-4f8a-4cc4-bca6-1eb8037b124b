using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Test script to validate the UI virtualization implementation
/// </summary>
public class VirtualizationTest : MonoBehaviour
{
    [Header("Test Settings")]
    public bool runTestOnStart = false;
    public bool enableDebugLogs = true;
    
    private ConfigsHandler configsHandler;
    private VirtualScrollController virtualScrollController;
    private CharacterDataProvider dataProvider;
    
    void Start()
    {
        if (runTestOnStart)
        {
            StartCoroutine(RunTests());
        }
    }
    
    System.Collections.IEnumerator RunTests()
    {
        yield return new WaitForSeconds(1f); // Wait for initialization
        
        LogDebug("=== Starting UI Virtualization Tests ===");
        
        // Test 1: Find required components
        yield return TestComponentsExist();
        
        // Test 2: Test data provider functionality
        yield return TestDataProvider();
        
        // Test 3: Test virtual scroll controller
        yield return TestVirtualScrollController();
        
        // Test 4: Test character UI pool
        yield return TestCharacterUIPool();
        
        LogDebug("=== UI Virtualization Tests Complete ===");
    }
    
    System.Collections.IEnumerator TestComponentsExist()
    {
        LogDebug("Test 1: Checking if required components exist...");
        
        // Find ConfigsHandler
        configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        if (configsHandler == null)
        {
            LogError("ConfigsHandler not found!");
            yield break;
        }
        LogDebug("✓ ConfigsHandler found");
        
        // Find CharScrollContent
        GameObject charScrollContent = GameObject.Find("CharScrollContent");
        if (charScrollContent == null)
        {
            LogError("CharScrollContent not found!");
            yield break;
        }
        LogDebug("✓ CharScrollContent found");
        
        // Check for VirtualScrollController
        virtualScrollController = charScrollContent.GetComponent<VirtualScrollController>();
        if (virtualScrollController == null)
        {
            LogWarning("VirtualScrollController not found - this is expected before setup");
        }
        else
        {
            LogDebug("✓ VirtualScrollController found");
        }
        
        yield return null;
    }
    
    System.Collections.IEnumerator TestDataProvider()
    {
        LogDebug("Test 2: Testing CharacterDataProvider...");
        
        if (configsHandler == null)
        {
            LogError("ConfigsHandler is null, cannot test data provider");
            yield break;
        }
        
        try
        {
            dataProvider = new CharacterDataProvider(configsHandler);
            
            if (!dataProvider.IsReady)
            {
                LogError("CharacterDataProvider is not ready");
                yield break;
            }
            
            int characterCount = dataProvider.GetCharacterCount();
            LogDebug($"✓ CharacterDataProvider ready with {characterCount} characters");
            
            // Test getting a character
            if (characterCount > 0)
            {
                var character = dataProvider.GetCharacter(0);
                if (character != null)
                {
                    LogDebug($"✓ Successfully retrieved character: {character.name}");
                }
                else
                {
                    LogError("Failed to retrieve character at index 0");
                }
            }
        }
        catch (System.Exception ex)
        {
            LogError($"CharacterDataProvider test failed: {ex.Message}");
        }
        
        yield return null;
    }
    
    System.Collections.IEnumerator TestVirtualScrollController()
    {
        LogDebug("Test 3: Testing VirtualScrollController...");
        
        GameObject charScrollContent = GameObject.Find("CharScrollContent");
        if (charScrollContent == null)
        {
            LogError("CharScrollContent not found for VirtualScrollController test");
            yield break;
        }
        
        try
        {
            // Get or add VirtualScrollController
            virtualScrollController = charScrollContent.GetComponent<VirtualScrollController>();
            if (virtualScrollController == null)
            {
                virtualScrollController = charScrollContent.AddComponent<VirtualScrollController>();
                LogDebug("✓ VirtualScrollController component added");
            }
            
            // Test initialization
            if (dataProvider != null)
            {
                virtualScrollController.Initialize(dataProvider);
                LogDebug($"✓ VirtualScrollController initialized with {virtualScrollController.TotalItemCount} items");
            }
            else
            {
                LogWarning("DataProvider is null, cannot initialize VirtualScrollController");
            }
        }
        catch (System.Exception ex)
        {
            LogError($"VirtualScrollController test failed: {ex.Message}");
        }
        
        yield return null;
    }
    
    System.Collections.IEnumerator TestCharacterUIPool()
    {
        LogDebug("Test 4: Testing CharacterUIPool...");
        
        try
        {
            Transform parentTransform = GameObject.Find("CharScrollContent")?.transform;
            if (parentTransform == null)
            {
                LogError("Parent transform not found for CharacterUIPool test");
                yield break;
            }
            
            CharacterUIPool pool = new CharacterUIPool(5, parentTransform);
            LogDebug($"✓ CharacterUIPool created with {pool.TotalCount} elements");
            
            // Test getting element from pool
            var element = pool.GetFromPool();
            if (element != null)
            {
                LogDebug("✓ Successfully retrieved element from pool");
                
                // Test returning element to pool
                pool.ReturnToPool(element);
                LogDebug("✓ Successfully returned element to pool");
            }
            else
            {
                LogError("Failed to retrieve element from pool");
            }
            
            // Cleanup
            pool.Dispose();
            LogDebug("✓ CharacterUIPool disposed");
        }
        catch (System.Exception ex)
        {
            LogError($"CharacterUIPool test failed: {ex.Message}");
        }
        
        yield return null;
    }
    
    void LogDebug(string message)
    {
        if (enableDebugLogs)
        {
            Debug.Log($"[VirtualizationTest] {message}");
        }
    }
    
    void LogWarning(string message)
    {
        Debug.LogWarning($"[VirtualizationTest] {message}");
    }
    
    void LogError(string message)
    {
        Debug.LogError($"[VirtualizationTest] {message}");
    }
    
    // Public methods for manual testing
    [ContextMenu("Run Tests")]
    public void RunTestsManual()
    {
        StartCoroutine(RunTests());
    }
    
    [ContextMenu("Test Data Provider")]
    public void TestDataProviderManual()
    {
        StartCoroutine(TestDataProvider());
    }
    
    [ContextMenu("Log Virtual Scroll Debug Info")]
    public void LogVirtualScrollDebugInfo()
    {
        if (virtualScrollController != null)
        {
            virtualScrollController.LogDebugInfo();
        }
        else
        {
            LogError("VirtualScrollController not found");
        }
    }
}
