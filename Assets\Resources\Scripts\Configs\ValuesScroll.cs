using UnityEngine;

public class ValuesScroll : MonoBehaviour
{
    public float pos = 0.45f; // end point of scroll


    private void Update()
    {
        // gets the configs camera
        Camera confCamera = GameObject.Find("Configs Camera").GetComponent<Camera>();


        // checks if the camera is active and there is a screen touch
        if (confCamera != null && confCamera.enabled && Input.touchCount > 0 && confCamera.ScreenToViewportPoint(Input.GetTouch(0).position).y < pos)
        {
            Vector3 movement = new(0f, Input.GetTouch(0).deltaPosition.y / (confCamera.pixelHeight / 10f), 0f);

            transform.Translate(movement);
        }
    }
}
