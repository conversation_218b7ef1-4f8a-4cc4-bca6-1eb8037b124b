using UnityEngine;
using UnityEngine.UI;

public class AddCharButton : MonoBehaviour
{
    ConfigsHandler configsHandler; // reference to the ConfigsHandler script

    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>(); // Get the ConfigsHandler script

        GetComponent<Button>().onClick.AddListener(onButtonPress); // Add a listener to the button that adds a character
    }

    void onButtonPress()
    {
        int index = configsHandler.AddCharacter(); // Add a character and get its index

        // With virtualization, the VirtualScrollController will automatically handle
        // the UI update through the CharacterDataProvider's OnDataChanged event
        // No need to create AddCharacter objects anymore

        Debug.Log($"[AddCharButton] Added character at index {index}");
    }
}
