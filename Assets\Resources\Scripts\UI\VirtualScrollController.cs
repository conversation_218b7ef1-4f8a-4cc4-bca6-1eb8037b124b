using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Virtual scroll controller that manages UI virtualization for large character lists.
/// Only renders visible items to improve performance with large datasets.
/// </summary>
public class VirtualScrollController : MonoBehaviour
{
    [Header("Virtualization Settings")]
    [SerializeField] private int maxVisibleItems = 14;
    [SerializeField] private float itemHeight = 50f;
    [SerializeField] private float spacing = 5f;
    
    [Header("References")]
    [SerializeField] private ScrollRect scrollRect;
    [SerializeField] private RectTransform content;
    [SerializeField] private RectTransform viewport;
    
    // Data and pooling
    private ICharacterDataProvider dataProvider;
    private CharacterUIPool uiPool;
    private List<VirtualCharacterItem> virtualItems = new List<VirtualCharacterItem>();
    
    // Scroll tracking
    private int firstVisibleIndex = 0;
    private int lastVisibleIndex = 0;
    private float lastScrollPosition = 0f;
    
    // Events
    public event Action<int> OnItemBecameVisible;
    public event Action<int> OnItemBecameInvisible;
    
    // Properties
    public int TotalItemCount => virtualItems.Count;
    public int VisibleItemCount => Mathf.Min(maxVisibleItems, TotalItemCount);
    public float TotalContentHeight => TotalItemCount > 0 ? TotalItemCount * (itemHeight + spacing) - spacing : 0f;
    
    private void Awake()
    {
        InitializeComponents();
    }
    
    private void Start()
    {
        SetupScrollRect();
    }
    
    private void InitializeComponents()
    {
        // The VirtualScrollController is attached to CharScrollContent
        // But the ScrollRect is on CharactersInfoScroll (grandparent)
        // Hierarchy: CharactersInfoScroll (ScrollRect) -> Viewport -> CharScrollContent (VirtualScrollController)

        // Find ScrollRect in parent hierarchy
        if (scrollRect == null)
            scrollRect = GetComponentInParent<ScrollRect>();

        if (scrollRect == null)
        {
            Debug.LogError("[VirtualScrollController] ScrollRect component not found in parent hierarchy!");
            return;
        }

        // The content should be this GameObject's RectTransform (CharScrollContent)
        if (content == null)
            content = GetComponent<RectTransform>();

        // Ensure the ScrollRect's content is set to this GameObject
        if (scrollRect.content != content)
        {
            scrollRect.content = content;
            Debug.Log("[VirtualScrollController] Updated ScrollRect.content to point to CharScrollContent");
        }

        if (viewport == null)
            viewport = scrollRect.viewport;

        // Initialize UI pool
        uiPool = new CharacterUIPool(maxVisibleItems, transform);

        // Setup scroll listener
        if (scrollRect != null)
            scrollRect.onValueChanged.AddListener(OnScrollValueChanged);

        Debug.Log($"[VirtualScrollController] Components initialized - ScrollRect: {scrollRect.name}, Content: {content.name}");
    }
    
    private void SetupScrollRect()
    {
        if (scrollRect == null) return;
        
        // Configure scroll rect for vertical scrolling
        scrollRect.horizontal = false;
        scrollRect.vertical = true;
        scrollRect.movementType = ScrollRect.MovementType.Clamped;
        scrollRect.scrollSensitivity = 20f;
        
        // Set content size
        UpdateContentSize();
    }
    
    /// <summary>
    /// Initialize the virtual scroll with character data provider
    /// </summary>
    public void Initialize(ICharacterDataProvider provider)
    {
        dataProvider = provider ?? throw new ArgumentNullException(nameof(provider));
        
        // Subscribe to data changes
        dataProvider.OnDataChanged += OnDataChanged;
        dataProvider.OnItemChanged += OnItemChanged;
        
        // Load initial data
        RefreshData();
    }
    
    /// <summary>
    /// Refresh all data and rebuild virtual items
    /// </summary>
    public void RefreshData()
    {
        if (dataProvider == null) return;

        // Clear existing virtual items
        virtualItems.Clear();

        // Create virtual items for all characters
        int characterCount = dataProvider.GetCharacterCount();

        // Debug: Check chunked storage info
        if (JsonSaveHelper.IsUsingChunkedStorage())
        {
            int chunkCount = JsonSaveHelper.GetChunkFileCount();
            int expectedCharacters = chunkCount * JsonSaveHelper.CHARACTERS_PER_CHUNK;
            Debug.Log($"[VirtualScrollController] Chunked storage detected: {chunkCount} chunks, " +
                     $"expected ~{expectedCharacters} characters, actual: {characterCount}");
        }

        for (int i = 0; i < characterCount; i++)
        {
            var character = dataProvider.GetCharacter(i);
            if (character != null)
            {
                virtualItems.Add(new VirtualCharacterItem(i, character));

                // Debug: Log sample characters from different chunks
                int chunkIndex = i / JsonSaveHelper.CHARACTERS_PER_CHUNK;
                if (i % 50 == 0 || i == characterCount - 1) // Log first character of each chunk and last character
                {
                    Debug.Log($"[VirtualScrollController] Sample character {i}: '{character.name}' (ID: {character.id}, Chunk: {chunkIndex})");
                }
            }
        }

        Debug.Log($"[VirtualScrollController] RefreshData complete - {virtualItems.Count} virtual items created from {characterCount} characters");

        // Verify character distribution across chunks
        VerifyCharacterDistribution();

        // Update content size and refresh visible items
        UpdateContentSize();
        UpdateVisibleRange(); // Calculate initial visible range
        RefreshVisibleItems();
    }
    
    private void UpdateContentSize()
    {
        if (content == null) return;

        float totalHeight = TotalContentHeight;
        Vector2 oldSize = content.sizeDelta;
        content.sizeDelta = new Vector2(content.sizeDelta.x, totalHeight);

        Debug.Log($"[VirtualScrollController] UpdateContentSize - TotalItems: {TotalItemCount}, " +
                  $"ItemHeight: {itemHeight}, Spacing: {spacing}, " +
                  $"TotalHeight: {totalHeight}, OldSize: {oldSize}, NewSize: {content.sizeDelta}");
    }
    
    private void OnScrollValueChanged(Vector2 scrollPosition)
    {
        float currentScrollY = content.anchoredPosition.y;

        // Only update if scroll position changed significantly
        if (Mathf.Abs(currentScrollY - lastScrollPosition) > 1f)
        {
            lastScrollPosition = currentScrollY;

            // Debug: Show scroll details
            Debug.Log($"[VirtualScrollController] Scroll changed - ScrollY: {currentScrollY}, " +
                     $"Content Height: {content.sizeDelta.y}, Viewport Height: {viewport.rect.height}");

            UpdateVisibleRange();
            RefreshVisibleItems();
        }
    }
    
    private void UpdateVisibleRange()
    {
        if (TotalItemCount == 0) return;

        float viewportHeight = viewport.rect.height;
        float scrollY = Mathf.Abs(content.anchoredPosition.y);

        // Calculate visible range with buffer for smooth scrolling
        int bufferItems = 2; // Extra items above/below for smooth scrolling

        firstVisibleIndex = Mathf.Max(0,
            Mathf.FloorToInt(scrollY / (itemHeight + spacing)) - bufferItems);

        lastVisibleIndex = Mathf.Min(TotalItemCount - 1,
            firstVisibleIndex + maxVisibleItems + (bufferItems * 2) - 1);

        Debug.Log($"[VirtualScrollController] UpdateVisibleRange - ScrollY: {scrollY}, ViewportHeight: {viewportHeight}, " +
                  $"FirstVisible: {firstVisibleIndex}, LastVisible: {lastVisibleIndex}, TotalItems: {TotalItemCount}, " +
                  $"VisibleCount: {lastVisibleIndex - firstVisibleIndex + 1}");
    }
    
    private void RefreshVisibleItems()
    {
        if (dataProvider == null) return;

        Debug.Log($"[VirtualScrollController] RefreshVisibleItems - Range: {firstVisibleIndex} to {lastVisibleIndex}, VirtualItems: {virtualItems.Count}");

        // For now, use simple approach: return all to pool and recreate
        // This ensures reliability while we debug the core issues
        uiPool.ReturnAllToPool();

        // Create UI elements for visible range
        int itemsCreated = 0;
        for (int i = firstVisibleIndex; i <= lastVisibleIndex; i++)
        {
            if (i >= 0 && i < virtualItems.Count)
            {
                CreateVisibleItem(i);
                itemsCreated++;
            }
        }

        Debug.Log($"[VirtualScrollController] Created {itemsCreated} visible items");

        // Force layout group to update immediately
        ForceLayoutUpdate();
    }



    /// <summary>
    /// Force the layout group to update immediately
    /// </summary>
    private void ForceLayoutUpdate()
    {
        if (content != null)
        {
            // Force layout rebuild
            LayoutRebuilder.ForceRebuildLayoutImmediate(content);
        }
    }

    private void CreateVisibleItem(int index)
    {
        if (index < 0 || index >= virtualItems.Count)
        {
            Debug.LogError($"[VirtualScrollController] Invalid index {index} for virtualItems (count: {virtualItems.Count})");
            return;
        }

        var virtualItem = virtualItems[index];
        if (virtualItem?.Character == null)
        {
            Debug.LogError($"[VirtualScrollController] VirtualItem at index {index} has null character");
            return;
        }

        var uiElement = uiPool.GetFromPool();
        if (uiElement != null)
        {
            // Set sibling index to maintain correct order in layout group
            uiElement.transform.SetSiblingIndex(index - firstVisibleIndex);

            // Bind character data to UI element
            uiElement.BindCharacter(virtualItem.Character, index);

            // Mark as visible
            virtualItem.IsVisible = true;
            OnItemBecameVisible?.Invoke(index);

            // Debug: Show character details including expected chunk and UI positioning
            int expectedChunk = index / JsonSaveHelper.CHARACTERS_PER_CHUNK;
            int siblingIndex = uiElement.transform.GetSiblingIndex();
            bool isActive = uiElement.gameObject.activeInHierarchy;

            Debug.Log($"[VirtualScrollController] Created visible item {index} for character '{virtualItem.Character.name}' " +
                     $"(ID: {virtualItem.Character.id}, Expected Chunk: {expectedChunk}, SiblingIndex: {siblingIndex}, Active: {isActive})");
        }
        else
        {
            Debug.LogError($"[VirtualScrollController] Failed to get UI element from pool for index {index}");
        }
    }
    
    private void OnDataChanged()
    {
        RefreshData();
    }
    
    private void OnItemChanged(int index)
    {
        // Update specific item if it's currently visible
        if (index >= firstVisibleIndex && index <= lastVisibleIndex)
        {
            RefreshVisibleItems();
        }
    }
    
    /// <summary>
    /// Scroll to specific character index
    /// </summary>
    public void ScrollToItem(int index)
    {
        if (index < 0 || index >= TotalItemCount)
        {
            Debug.LogWarning($"[VirtualScrollController] ScrollToItem: Invalid index {index} (valid range: 0-{TotalItemCount - 1})");
            return;
        }

        float targetY = index * (itemHeight + spacing);
        float viewportHeight = viewport.rect.height;

        // Clamp to valid scroll range
        float maxScrollY = Mathf.Max(0, TotalContentHeight - viewportHeight);
        targetY = Mathf.Clamp(targetY, 0, maxScrollY);

        Debug.Log($"[VirtualScrollController] ScrollToItem {index}: targetY={targetY}, maxScrollY={maxScrollY}, contentHeight={TotalContentHeight}");

        // Set scroll position
        content.anchoredPosition = new Vector2(content.anchoredPosition.x, targetY);

        // Update visible items
        UpdateVisibleRange();
        RefreshVisibleItems();
    }

    /// <summary>
    /// Test method to scroll to different positions and verify character access
    /// </summary>
    [ContextMenu("Test Scroll Positions")]
    public void TestScrollPositions()
    {
        Debug.Log("[VirtualScrollController] Testing scroll positions...");

        // Test scrolling to different chunks
        int[] testIndices = { 0, 50, 100, 200, 300, 400, 500, 600, TotalItemCount - 1 };

        foreach (int index in testIndices)
        {
            if (index < TotalItemCount)
            {
                int expectedChunk = index / JsonSaveHelper.CHARACTERS_PER_CHUNK;
                Debug.Log($"[VirtualScrollController] Testing scroll to index {index} (expected chunk {expectedChunk})");
                ScrollToItem(index);

                // Log what characters are actually visible
                for (int i = firstVisibleIndex; i <= lastVisibleIndex && i < virtualItems.Count; i++)
                {
                    if (virtualItems[i]?.Character != null)
                    {
                        int actualChunk = i / JsonSaveHelper.CHARACTERS_PER_CHUNK;
                        Debug.Log($"  Visible: {i} - '{virtualItems[i].Character.name}' (chunk {actualChunk})");
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// Get character index at specific scroll position
    /// </summary>
    public int GetItemIndexAtPosition(Vector2 position)
    {
        float localY = Mathf.Abs(position.y);
        return Mathf.FloorToInt(localY / (itemHeight + spacing));
    }
    
    private void OnDestroy()
    {
        // Cleanup
        if (dataProvider != null)
        {
            dataProvider.OnDataChanged -= OnDataChanged;
            dataProvider.OnItemChanged -= OnItemChanged;
        }
        
        if (scrollRect != null)
            scrollRect.onValueChanged.RemoveListener(OnScrollValueChanged);
            
        uiPool?.Dispose();
    }
    
    /// <summary>
    /// Verify that characters are properly distributed across all chunks
    /// </summary>
    private void VerifyCharacterDistribution()
    {
        if (virtualItems.Count == 0) return;

        Debug.Log($"[VirtualScrollController] Verifying character distribution across {virtualItems.Count} items...");

        // Check characters from different chunks
        var chunkSamples = new Dictionary<int, List<string>>();

        for (int i = 0; i < virtualItems.Count; i++)
        {
            if (virtualItems[i]?.Character != null)
            {
                int expectedChunk = i / JsonSaveHelper.CHARACTERS_PER_CHUNK;

                if (!chunkSamples.ContainsKey(expectedChunk))
                    chunkSamples[expectedChunk] = new List<string>();

                if (chunkSamples[expectedChunk].Count < 3) // Sample first 3 from each chunk
                {
                    chunkSamples[expectedChunk].Add($"{i}:{virtualItems[i].Character.name}");
                }
            }
        }

        foreach (var kvp in chunkSamples)
        {
            Debug.Log($"[VirtualScrollController] Chunk {kvp.Key} samples: {string.Join(", ", kvp.Value)}");
        }
    }

    // Debug information
    public void LogDebugInfo()
    {
        Debug.Log($"Virtual Scroll Debug - Total: {TotalItemCount}, " +
                  $"Visible Range: {firstVisibleIndex}-{lastVisibleIndex}, " +
                  $"Active UI Elements: {uiPool?.ActiveCount ?? 0}");
    }

    /// <summary>
    /// Debug method to check scroll system state
    /// </summary>
    [ContextMenu("Debug Scroll State")]
    public void DebugScrollState()
    {
        Debug.Log("=== VIRTUAL SCROLL DEBUG STATE ===");
        Debug.Log($"TotalItemCount: {TotalItemCount}");
        Debug.Log($"MaxVisibleItems: {maxVisibleItems}");
        Debug.Log($"ItemHeight: {itemHeight}, Spacing: {spacing}");
        Debug.Log($"TotalContentHeight: {TotalContentHeight}");

        if (content != null)
        {
            Debug.Log($"Content Size: {content.sizeDelta}");
            Debug.Log($"Content Position: {content.anchoredPosition}");
        }

        if (viewport != null)
        {
            Debug.Log($"Viewport Size: {viewport.rect.size}");
        }

        if (scrollRect != null)
        {
            Debug.Log($"ScrollRect Vertical: {scrollRect.vertical}");
            Debug.Log($"ScrollRect Horizontal: {scrollRect.horizontal}");
            Debug.Log($"ScrollRect Movement: {scrollRect.movementType}");
            Debug.Log($"ScrollRect Sensitivity: {scrollRect.scrollSensitivity}");
        }

        Debug.Log($"Current Visible Range: {firstVisibleIndex} to {lastVisibleIndex}");
        Debug.Log($"Active UI Pool Count: {uiPool?.ActiveCount ?? 0}");
        Debug.Log("=== END DEBUG STATE ===");
    }
}
