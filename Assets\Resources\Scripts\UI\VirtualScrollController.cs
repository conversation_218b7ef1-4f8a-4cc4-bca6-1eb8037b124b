using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Virtual scroll controller that manages UI virtualization for large character lists.
/// Only renders visible items to improve performance with large datasets.
/// </summary>
public class VirtualScrollController : MonoBehaviour
{
    [Header("Virtualization Settings")]
    [SerializeField] private int maxVisibleItems = 14;
    [SerializeField] private float itemHeight = 50f;
    [SerializeField] private float spacing = 5f;
    
    [Header("References")]
    [SerializeField] private ScrollRect scrollRect;
    [SerializeField] private RectTransform content;
    [SerializeField] private RectTransform viewport;
    
    // Data and pooling
    private ICharacterDataProvider dataProvider;
    private CharacterUIPool uiPool;
    private List<VirtualCharacterItem> virtualItems = new List<VirtualCharacterItem>();
    
    // Scroll tracking
    private int firstVisibleIndex = 0;
    private int lastVisibleIndex = 0;
    private float lastScrollPosition = 0f;
    
    // Events
    public event Action<int> OnItemBecameVisible;
    public event Action<int> OnItemBecameInvisible;
    
    // Properties
    public int TotalItemCount => virtualItems.Count;
    public int VisibleItemCount => Mathf.Min(maxVisibleItems, TotalItemCount);
    public float TotalContentHeight => TotalItemCount * (itemHeight + spacing) - spacing;
    
    private void Awake()
    {
        InitializeComponents();
    }
    
    private void Start()
    {
        SetupScrollRect();
    }
    
    private void InitializeComponents()
    {
        // Get or create required components
        if (scrollRect == null)
            scrollRect = GetComponent<ScrollRect>();
        
        if (content == null)
            content = scrollRect.content;
            
        if (viewport == null)
            viewport = scrollRect.viewport;
            
        // Initialize UI pool
        uiPool = new CharacterUIPool(maxVisibleItems, transform);
        
        // Setup scroll listener
        if (scrollRect != null)
            scrollRect.onValueChanged.AddListener(OnScrollValueChanged);
    }
    
    private void SetupScrollRect()
    {
        if (scrollRect == null) return;
        
        // Configure scroll rect for vertical scrolling
        scrollRect.horizontal = false;
        scrollRect.vertical = true;
        scrollRect.movementType = ScrollRect.MovementType.Clamped;
        scrollRect.scrollSensitivity = 20f;
        
        // Set content size
        UpdateContentSize();
    }
    
    /// <summary>
    /// Initialize the virtual scroll with character data provider
    /// </summary>
    public void Initialize(ICharacterDataProvider provider)
    {
        dataProvider = provider ?? throw new ArgumentNullException(nameof(provider));
        
        // Subscribe to data changes
        dataProvider.OnDataChanged += OnDataChanged;
        dataProvider.OnItemChanged += OnItemChanged;
        
        // Load initial data
        RefreshData();
    }
    
    /// <summary>
    /// Refresh all data and rebuild virtual items
    /// </summary>
    public void RefreshData()
    {
        if (dataProvider == null) return;
        
        // Clear existing virtual items
        virtualItems.Clear();
        
        // Create virtual items for all characters
        int characterCount = dataProvider.GetCharacterCount();
        for (int i = 0; i < characterCount; i++)
        {
            var character = dataProvider.GetCharacter(i);
            if (character != null)
            {
                virtualItems.Add(new VirtualCharacterItem(i, character));
            }
        }
        
        // Update content size and refresh visible items
        UpdateContentSize();
        RefreshVisibleItems();
    }
    
    private void UpdateContentSize()
    {
        if (content == null) return;
        
        float totalHeight = TotalContentHeight;
        content.sizeDelta = new Vector2(content.sizeDelta.x, totalHeight);
    }
    
    private void OnScrollValueChanged(Vector2 scrollPosition)
    {
        float currentScrollY = content.anchoredPosition.y;
        
        // Only update if scroll position changed significantly
        if (Mathf.Abs(currentScrollY - lastScrollPosition) > 1f)
        {
            lastScrollPosition = currentScrollY;
            UpdateVisibleRange();
            RefreshVisibleItems();
        }
    }
    
    private void UpdateVisibleRange()
    {
        if (TotalItemCount == 0) return;
        
        float viewportHeight = viewport.rect.height;
        float scrollY = Mathf.Abs(content.anchoredPosition.y);
        
        // Calculate visible range with buffer
        int bufferItems = 2; // Extra items above/below for smooth scrolling
        
        firstVisibleIndex = Mathf.Max(0, 
            Mathf.FloorToInt(scrollY / (itemHeight + spacing)) - bufferItems);
            
        lastVisibleIndex = Mathf.Min(TotalItemCount - 1,
            Mathf.CeilToInt((scrollY + viewportHeight) / (itemHeight + spacing)) + bufferItems);
    }
    
    private void RefreshVisibleItems()
    {
        if (dataProvider == null) return;
        
        // Return all UI elements to pool
        uiPool.ReturnAllToPool();
        
        // Create UI elements for visible range
        for (int i = firstVisibleIndex; i <= lastVisibleIndex; i++)
        {
            if (i >= 0 && i < virtualItems.Count)
            {
                CreateVisibleItem(i);
            }
        }
    }
    
    private void CreateVisibleItem(int index)
    {
        var virtualItem = virtualItems[index];
        var uiElement = uiPool.GetFromPool();
        
        if (uiElement != null)
        {
            // Position the UI element
            float yPosition = -(index * (itemHeight + spacing));
            uiElement.SetPosition(new Vector2(0, yPosition));
            
            // Bind character data to UI element
            uiElement.BindCharacter(virtualItem.Character, index);
            
            // Mark as visible
            virtualItem.IsVisible = true;
            OnItemBecameVisible?.Invoke(index);
        }
    }
    
    private void OnDataChanged()
    {
        RefreshData();
    }
    
    private void OnItemChanged(int index)
    {
        // Update specific item if it's currently visible
        if (index >= firstVisibleIndex && index <= lastVisibleIndex)
        {
            RefreshVisibleItems();
        }
    }
    
    /// <summary>
    /// Scroll to specific character index
    /// </summary>
    public void ScrollToItem(int index)
    {
        if (index < 0 || index >= TotalItemCount) return;
        
        float targetY = index * (itemHeight + spacing);
        float viewportHeight = viewport.rect.height;
        
        // Clamp to valid scroll range
        float maxScrollY = Mathf.Max(0, TotalContentHeight - viewportHeight);
        targetY = Mathf.Clamp(targetY, 0, maxScrollY);
        
        // Set scroll position
        content.anchoredPosition = new Vector2(content.anchoredPosition.x, targetY);
        
        // Update visible items
        UpdateVisibleRange();
        RefreshVisibleItems();
    }
    
    /// <summary>
    /// Get character index at specific scroll position
    /// </summary>
    public int GetItemIndexAtPosition(Vector2 position)
    {
        float localY = Mathf.Abs(position.y);
        return Mathf.FloorToInt(localY / (itemHeight + spacing));
    }
    
    private void OnDestroy()
    {
        // Cleanup
        if (dataProvider != null)
        {
            dataProvider.OnDataChanged -= OnDataChanged;
            dataProvider.OnItemChanged -= OnItemChanged;
        }
        
        if (scrollRect != null)
            scrollRect.onValueChanged.RemoveListener(OnScrollValueChanged);
            
        uiPool?.Dispose();
    }
    
    // Debug information
    public void LogDebugInfo()
    {
        Debug.Log($"Virtual Scroll Debug - Total: {TotalItemCount}, " +
                  $"Visible Range: {firstVisibleIndex}-{lastVisibleIndex}, " +
                  $"Active UI Elements: {uiPool?.ActiveCount ?? 0}");
    }
}
