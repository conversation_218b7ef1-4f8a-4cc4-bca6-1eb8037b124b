using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ChangeSelectedColorCharPrefab : MonoBehaviour
{
    public Color color = Color.green;

    void Update() // changes the color of the Object to show that is selected by using a Child object
    {
        if (transform.GetChild(0).gameObject.activeSelf)
        {
            GetComponent<Image>().color = color;
            transform.GetChild(1).GetComponent<TextMeshProUGUI>().color = color;
        }
        else
        {
            GetComponent<Image>().color = Color.white;
            transform.GetChild(1).GetComponent<TextMeshProUGUI>().color = Color.white;
        }
    }
}
